"""
测试复杂障碍物环境
验证静态和动态障碍物的生成和运行，展示实际的导航测试
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import time
import random

from simple_environment import SimpleUAVEnvironment, EnvironmentVisualizer
from dwa_rl_core import StabilizedEnvironment
from td3_dwa_rl_architecture import SafeActionSetGenerator, td3_config

def test_static_complex_environment():
    """测试静态复杂障碍物环境"""
    print("🧪 测试静态复杂障碍物环境")
    print("=" * 50)
    
    # 创建环境
    env = SimpleUAVEnvironment(enable_dynamic_obstacles=False)
    visualizer = EnvironmentVisualizer(env)
    
    # 打印环境布局
    visualizer.print_environment_layout()
    
    # 简单测试步进
    state = env.reset()
    print(f"\n初始观测维度: {len(state)}")
    print(f"障碍物数量: {len(env.obstacles)}")
    
    # 测试几步随机动作
    for i in range(5):
        action = np.random.uniform(-1, 1, 3)
        next_state, reward, done, info = env.step(action)
        print(f"步骤 {i+1}: 奖励={reward:.3f}, 完成={done}")
        if done:
            break
    
    return env

def test_dynamic_obstacle_movement():
    """专门测试动态障碍物运动效果"""
    print("\n🔄 动态障碍物运动演示")
    print("=" * 50)

    # 创建动态环境
    env = SimpleUAVEnvironment(enable_dynamic_obstacles=True)

    print(f"生成了 {len(env.dynamic_obstacles)} 个动态障碍物:")
    for i, obs in enumerate(env.dynamic_obstacles):
        print(f"  动态障碍物 {i+1}: {obs['motion_type']} 运动, 半径 {obs['radius']:.1f}")

    # 记录初始位置
    initial_positions = []
    for obs in env.dynamic_obstacles:
        initial_positions.append(obs['center'].copy())

    print("\n🎬 动态障碍物运动轨迹 (30步演示):")
    print("-" * 60)

    # 创建轨迹记录
    trajectories = {i: [] for i in range(len(env.dynamic_obstacles))}

    # 模拟30步运动
    for step in range(30):
        env._update_dynamic_obstacles()

        # 记录轨迹
        for i, obs in enumerate(env.dynamic_obstacles):
            trajectories[i].append(obs['center'].copy())

        # 每5步打印一次位置
        if step % 5 == 0:
            print(f"\n步骤 {step + 1}:")
            for i, obs in enumerate(env.dynamic_obstacles):
                center = obs['center']
                initial = initial_positions[i]
                displacement = np.linalg.norm(center - initial)
                print(f"  障碍物{i+1} ({obs['motion_type']}): "
                      f"[{center[0]:.1f}, {center[1]:.1f}, {center[2]:.1f}] "
                      f"(总位移: {displacement:.1f}m)")

        time.sleep(0.05)  # 模拟时间流逝

    # 分析运动特征
    print(f"\n📊 运动分析:")
    print("-" * 30)
    for i, obs in enumerate(env.dynamic_obstacles):
        trajectory = np.array(trajectories[i])
        total_distance = 0
        for j in range(1, len(trajectory)):
            total_distance += np.linalg.norm(trajectory[j] - trajectory[j-1])

        final_displacement = np.linalg.norm(trajectory[-1] - trajectory[0])
        print(f"障碍物{i+1} ({obs['motion_type']}):")
        print(f"  总移动距离: {total_distance:.1f}m")
        print(f"  最终位移: {final_displacement:.1f}m")
        print(f"  平均速度: {total_distance/30:.2f}m/step")

    return env, trajectories

def test_navigation_with_dwa():
    """测试使用DWA进行导航"""
    print("\n🧭 DWA导航测试")
    print("=" * 50)

    # 创建环境和DWA控制器
    env = SimpleUAVEnvironment(enable_dynamic_obstacles=True)
    dwa = SafeActionSetGenerator(td3_config['dwa'])

    print(f"环境设置:")
    print(f"  静态障碍物: {len(env.obstacles)}个")
    print(f"  动态障碍物: {len(env.dynamic_obstacles)}个")
    print(f"  起点: [{env.start[0]:.1f}, {env.start[1]:.1f}, {env.start[2]:.1f}]")
    print(f"  终点: [{env.goal[0]:.1f}, {env.goal[1]:.1f}, {env.goal[2]:.1f}]")

    # 重置环境
    state = env.reset()
    current_state = np.concatenate([env.state, [0, 0, 0]])  # 添加加速度状态

    trajectory = []
    step_count = 0
    max_steps = 200

    print(f"\n� 开始导航 (最大 {max_steps} 步):")
    print("-" * 40)

    while step_count < max_steps:
        # 记录轨迹
        trajectory.append(env.state[:3].copy())

        # 使用DWA生成安全动作
        safe_actions = dwa.generate_safe_action_set(
            current_state, env.goal, env.obstacles + env.dynamic_obstacles
        )

        if not safe_actions:
            print(f"❌ 步骤 {step_count}: 无安全动作可选")
            break

        # 选择最佳动作（朝向目标的最优动作）
        best_action = max(safe_actions, key=lambda x: x['heading_score'])
        action = best_action['velocity']

        # 执行动作
        next_state, reward, done, info = env.step(action)
        current_state = np.concatenate([env.state, action])  # 更新状态

        step_count += 1

        # 每10步打印一次进度
        if step_count % 10 == 0:
            goal_dist = np.linalg.norm(env.state[:3] - env.goal)
            print(f"步骤 {step_count}: 距离目标 {goal_dist:.1f}m, 奖励 {reward:.2f}")

        if done:
            if info.get('success', False):
                print(f"✅ 成功到达目标! 用时 {step_count} 步")
            elif info.get('collision', False):
                print(f"💥 发生碰撞! 步骤 {step_count}")
            else:
                print(f"⏰ 其他结束条件: {info}")
            break

    if step_count >= max_steps:
        print(f"⏰ 达到最大步数限制")

    # 分析导航结果
    final_distance = np.linalg.norm(env.state[:3] - env.goal)
    path_length = 0
    if len(trajectory) > 1:
        for i in range(1, len(trajectory)):
            path_length += np.linalg.norm(np.array(trajectory[i]) - np.array(trajectory[i-1]))

    print(f"\n📊 导航分析:")
    print(f"  总步数: {step_count}")
    print(f"  路径长度: {path_length:.1f}m")
    print(f"  直线距离: {np.linalg.norm(env.goal - env.start):.1f}m")
    print(f"  路径效率: {np.linalg.norm(env.goal - env.start)/path_length:.2%}" if path_length > 0 else "  路径效率: N/A")
    print(f"  最终距离目标: {final_distance:.1f}m")

    return env, trajectory

def visualize_environment_3d(env, title="Complex Environment"):
    """3D可视化环境"""
    fig = plt.figure(figsize=(12, 10))
    ax = fig.add_subplot(111, projection='3d')
    
    # 设置起点和终点
    start = env.start
    goal = env.goal
    ax.scatter(start[0], start[1], start[2], c='green', s=200, marker='o', label='Start', alpha=0.8)
    ax.scatter(goal[0], goal[1], goal[2], c='red', s=200, marker='*', label='Goal', alpha=0.8)
    
    # 绘制静态障碍物
    for i, obs in enumerate(env.obstacles):
        center = obs['center']
        radius = obs['radius']
        
        # 创建球面
        u = np.linspace(0, 2 * np.pi, 20)
        v = np.linspace(0, np.pi, 20)
        x = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
        y = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
        z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]
        ax.plot_surface(x, y, z, alpha=0.3, color='gray')
    
    # 绘制动态障碍物（如果有）
    if hasattr(env, 'dynamic_obstacles') and env.dynamic_obstacles:
        for i, obs in enumerate(env.dynamic_obstacles):
            center = obs['center']
            radius = obs['radius']
            
            # 创建球面
            u = np.linspace(0, 2 * np.pi, 15)
            v = np.linspace(0, np.pi, 15)
            x = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
            y = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
            z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]
            ax.plot_surface(x, y, z, alpha=0.5, color='orange')
    
    # 设置坐标轴
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.set_zlabel('Z (m)')
    ax.set_xlim(0, 100)
    ax.set_ylim(0, 100)
    ax.set_zlim(0, 100)
    ax.grid(True, alpha=0.3)
    ax.legend()
    ax.set_title(title)
    
    plt.tight_layout()
    plt.show()
    
    return fig, ax

def compare_environments():
    """比较简单环境和复杂环境"""
    print("\n📊 环境复杂度比较")
    print("=" * 50)
    
    # 简单环境（原始）
    simple_env = SimpleUAVEnvironment(enable_dynamic_obstacles=False)
    # 手动设置为原始简单配置
    simple_env.obstacles = []
    for i in range(3):
        center = np.array([[30, 30, 30], [50, 20, 40], [40, 60, 50]][i], dtype=np.float64)
        radius = 6.0
        simple_env.obstacles.append({'center': center, 'radius': radius})
    
    # 复杂静态环境
    complex_static_env = SimpleUAVEnvironment(enable_dynamic_obstacles=False)
    
    # 复杂动态环境
    complex_dynamic_env = SimpleUAVEnvironment(enable_dynamic_obstacles=True)
    
    # 统计比较
    environments = [
        ("简单环境", simple_env),
        ("复杂静态环境", complex_static_env),
        ("复杂动态环境", complex_dynamic_env)
    ]
    
    for name, env in environments:
        static_count = len(env.obstacles)
        dynamic_count = len(getattr(env, 'dynamic_obstacles', []))
        total_count = static_count + dynamic_count
        
        print(f"{name}:")
        print(f"  静态障碍物: {static_count}")
        print(f"  动态障碍物: {dynamic_count}")
        print(f"  总障碍物: {total_count}")
        print(f"  观测维度: {len(env._get_observation())}")
        print()

def create_dynamic_visualization(env, trajectories):
    """创建动态障碍物运动轨迹可视化"""
    fig = plt.figure(figsize=(15, 10))
    ax = fig.add_subplot(111, projection='3d')

    # 设置起点和终点
    start = env.start
    goal = env.goal
    ax.scatter(start[0], start[1], start[2], c='green', s=200, marker='o', label='Start', alpha=0.8)
    ax.scatter(goal[0], goal[1], goal[2], c='red', s=200, marker='*', label='Goal', alpha=0.8)

    # 绘制静态障碍物
    for obs in env.obstacles:
        center = obs['center']
        radius = obs['radius']
        u = np.linspace(0, 2 * np.pi, 15)
        v = np.linspace(0, np.pi, 15)
        x = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
        y = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
        z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]
        ax.plot_surface(x, y, z, alpha=0.3, color='gray')

    # 绘制动态障碍物轨迹
    colors = ['orange', 'purple', 'brown', 'pink', 'cyan']
    for i, trajectory in trajectories.items():
        if trajectory:
            trajectory = np.array(trajectory)
            color = colors[i % len(colors)]

            # 绘制轨迹线
            ax.plot(trajectory[:, 0], trajectory[:, 1], trajectory[:, 2],
                   color=color, linewidth=2, alpha=0.7, label=f'动态障碍物{i+1}轨迹')

            # 绘制起始位置
            ax.scatter(trajectory[0, 0], trajectory[0, 1], trajectory[0, 2],
                      c=color, s=100, marker='o', alpha=0.8)

            # 绘制结束位置
            ax.scatter(trajectory[-1, 0], trajectory[-1, 1], trajectory[-1, 2],
                      c=color, s=100, marker='s', alpha=0.8)

    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.set_zlabel('Z (m)')
    ax.set_xlim(0, 100)
    ax.set_ylim(0, 100)
    ax.set_zlim(0, 100)
    ax.grid(True, alpha=0.3)
    ax.legend()
    ax.set_title('动态障碍物运动轨迹')

    plt.tight_layout()
    plt.savefig('dynamic_obstacles_trajectory.png', dpi=300, bbox_inches='tight')
    plt.show()

    return fig, ax

if __name__ == "__main__":
    print("🚀 复杂障碍物环境完整测试")
    print("=" * 60)

    # 1. 测试静态复杂环境
    print("📋 测试项目:")
    print("1. 静态复杂环境基础测试")
    print("2. 动态障碍物运动演示")
    print("3. DWA导航测试")
    print("4. 环境对比分析")
    print("5. 可视化展示")

    static_env = test_static_complex_environment()

    # 2. 测试动态障碍物运动
    dynamic_env, trajectories = test_dynamic_obstacle_movement()

    # 3. 测试DWA导航
    nav_env, nav_trajectory = test_navigation_with_dwa()

    # 4. 环境比较
    compare_environments()

    # 5. 可视化
    print("\n🎨 生成可视化图表")
    print("=" * 50)

    try:
        # 动态障碍物轨迹可视化
        print("正在生成动态障碍物轨迹图...")
        create_dynamic_visualization(dynamic_env, trajectories)
        print("✅ 动态轨迹图已保存: dynamic_obstacles_trajectory.png")

        # 环境对比可视化
        print("正在生成环境对比图...")
        visualize_environment_3d(static_env, "Complex Static Environment")
        visualize_environment_3d(dynamic_env, "Complex Dynamic Environment")

    except Exception as e:
        print(f"⚠️ 可视化失败: {e}")

    print("\n✅ 完整测试完成!")
    print("\n📊 测试总结:")
    print("- 静态环境: 验证了复杂障碍物生成")
    print("- 动态环境: 展示了障碍物运动轨迹")
    print("- DWA导航: 测试了实际导航能力")
    print("- 可视化: 生成了轨迹和环境对比图")
