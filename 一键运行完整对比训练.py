"""
一键运行完整奖励函数对比训练
点击运行即可开始4000轮完整训练对比
"""

import sys
import os
from datetime import datetime

def main():
    print("🚀 一键运行完整奖励函数对比训练")
    print("=" * 60)
    print("📋 训练计划:")
    print("  • 简化奖励函数分阶段训练: 2000轮")
    print("    - 阶段1 (基础): 500轮")
    print("    - 阶段2 (复杂静态): 1000轮") 
    print("    - 阶段3 (动态适应): 500轮")
    print("  • 复杂奖励函数分阶段训练: 2000轮")
    print("    - 阶段1 (基础): 500轮")
    print("    - 阶段2 (复杂静态): 1000轮")
    print("    - 阶段3 (动态适应): 500轮")
    print("  • 总计: 4000轮训练")
    print("=" * 60)
    
    # 确认开始训练
    response = input("🤔 确认开始训练吗？预计需要较长时间 (y/n): ")
    if response.lower() != 'y':
        print("❌ 训练已取消")
        return
    
    print(f"\n⏰ 训练开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔄 正在启动完整对比训练...")
    
    try:
        # 导入并运行完整训练
        from complete_reward_comparison_training import CompleteRewardComparisonTrainer
        
        trainer = CompleteRewardComparisonTrainer()
        trainer.run_complete_training()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保 complete_reward_comparison_training.py 文件存在")
    except Exception as e:
        print(f"❌ 训练过程中发生错误: {e}")
        print("请检查环境配置和依赖包")

if __name__ == "__main__":
    main()
