"""
简化奖励函数测试脚本
快速验证701文件夹中的简化奖励函数是否正确集成到主文件夹中
"""

import numpy as np
from dwa_rl_core import StabilizedEnvironment
from environment_config import get_environment_config

def test_reward_function_integration():
    """测试奖励函数集成是否正确"""
    print("🧪 测试简化奖励函数集成")
    print("=" * 50)
    
    # 测试1: 创建简化奖励函数环境
    print("📋 测试1: 创建简化奖励函数环境")
    try:
        env_simplified = StabilizedEnvironment(
            enable_dynamic_obstacles=False,
            environment_config=None,
            reward_type='simplified'
        )
        print("✅ 简化奖励函数环境创建成功")
        print(f"   奖励类型: {env_simplified.reward_type}")
    except Exception as e:
        print(f"❌ 简化奖励函数环境创建失败: {e}")
        return False
    
    # 测试2: 创建复杂奖励函数环境
    print("\n📋 测试2: 创建复杂奖励函数环境")
    try:
        env_complex = StabilizedEnvironment(
            enable_dynamic_obstacles=False,
            environment_config=None,
            reward_type='complex'
        )
        print("✅ 复杂奖励函数环境创建成功")
        print(f"   奖励类型: {env_complex.reward_type}")
    except Exception as e:
        print(f"❌ 复杂奖励函数环境创建失败: {e}")
        return False
    
    # 测试3: 验证奖励函数计算
    print("\n📋 测试3: 验证奖励函数计算")
    
    # 重置环境
    obs_simplified = env_simplified.reset()
    obs_complex = env_complex.reset()
    
    print(f"环境重置成功，观测维度: {len(obs_simplified)}")
    
    # 测试几个步骤
    test_actions = [
        np.array([1.0, 1.0, 1.0]),  # 向目标移动
        np.array([0.0, 0.0, 0.0]),  # 静止
        np.array([-1.0, -1.0, -1.0])  # 远离目标
    ]
    
    print("\n奖励函数对比测试:")
    print("动作类型          简化奖励    复杂奖励")
    print("-" * 40)
    
    for i, action in enumerate(test_actions):
        # 简化奖励函数
        obs_s, reward_s, done_s, info_s = env_simplified.step(action)
        
        # 复杂奖励函数  
        obs_c, reward_c, done_c, info_c = env_complex.step(action)
        
        action_names = ["向目标移动", "静止不动", "远离目标"]
        print(f"{action_names[i]:12} {reward_s:8.3f}   {reward_c:8.3f}")
        
        if done_s or done_c:
            print(f"Episode结束: 简化={info_s.get('reason', 'unknown')}, 复杂={info_c.get('reason', 'unknown')}")
            break
    
    # 测试4: 验证环境配置集成
    print("\n📋 测试4: 验证环境配置集成")
    try:
        from environment_config import TRAINING_STAGES
        
        # 检查简化奖励函数配置
        stage1_config = TRAINING_STAGES.get('stage1_basic', {})
        if 'reward_type' in stage1_config:
            print(f"✅ 阶段1配置包含奖励类型: {stage1_config['reward_type']}")
        else:
            print("⚠️ 阶段1配置缺少奖励类型，将使用默认值")
        
        # 检查复杂奖励函数对比配置
        stage1_complex_config = TRAINING_STAGES.get('stage1_basic_complex', {})
        if stage1_complex_config:
            print(f"✅ 找到复杂奖励函数对比配置: {stage1_complex_config['reward_type']}")
        else:
            print("⚠️ 未找到复杂奖励函数对比配置")
            
    except Exception as e:
        print(f"❌ 环境配置测试失败: {e}")
        return False
    
    # 测试5: 验证训练函数集成
    print("\n📋 测试5: 验证训练函数集成")
    try:
        from train_dwa_rl import train_dwa_rl_model
        
        # 检查函数签名是否包含reward_type参数
        import inspect
        sig = inspect.signature(train_dwa_rl_model)
        if 'reward_type' in sig.parameters:
            print("✅ 训练函数支持reward_type参数")
            print(f"   默认值: {sig.parameters['reward_type'].default}")
        else:
            print("❌ 训练函数缺少reward_type参数")
            return False
            
    except Exception as e:
        print(f"❌ 训练函数测试失败: {e}")
        return False
    
    print("\n🎉 所有测试通过！简化奖励函数集成成功")
    return True

def test_reward_function_behavior():
    """测试奖励函数行为特性"""
    print("\n🔬 测试奖励函数行为特性")
    print("=" * 50)
    
    # 创建测试环境
    env = StabilizedEnvironment(reward_type='simplified')
    env.reset()
    
    # 测试场景1: 接近目标
    print("📋 场景1: 接近目标")
    original_pos = env.state[:3].copy()
    goal_pos = env.goal.copy()
    
    # 设置位置接近目标
    env.state[:3] = goal_pos - np.array([10.0, 10.0, 10.0])
    reward1, done1, info1 = env._calculate_reward()
    print(f"距离目标10单位时奖励: {reward1:.3f}")
    
    # 更接近目标
    env.state[:3] = goal_pos - np.array([3.0, 3.0, 3.0])
    reward2, done2, info2 = env._calculate_reward()
    print(f"距离目标3单位时奖励: {reward2:.3f}")
    
    # 到达目标
    env.state[:3] = goal_pos - np.array([1.0, 1.0, 1.0])
    reward3, done3, info3 = env._calculate_reward()
    print(f"到达目标时奖励: {reward3:.3f}, 完成: {done3}")
    
    # 测试场景2: 碰撞检测
    print("\n📋 场景2: 碰撞检测")
    env.reset()
    
    # 设置位置在障碍物内
    if env.obstacles:
        obs_center = env.obstacles[0]['center']
        env.state[:3] = obs_center  # 直接设置在障碍物中心
        reward4, done4, info4 = env._calculate_reward()
        print(f"碰撞时奖励: {reward4:.3f}, 完成: {done4}")
        print(f"碰撞信息: {info4}")
    
    # 测试场景3: 安全距离
    print("\n📋 场景3: 安全距离测试")
    env.reset()
    
    if env.obstacles:
        obs_center = env.obstacles[0]['center']
        obs_radius = env.obstacles[0]['radius']
        
        # 安全距离
        safe_pos = obs_center + np.array([obs_radius + 5.0, 0, 0])
        env.state[:3] = safe_pos
        reward5, done5, info5 = env._calculate_reward()
        print(f"安全距离时奖励: {reward5:.3f}")
        
        # 危险距离
        danger_pos = obs_center + np.array([obs_radius + 1.0, 0, 0])
        env.state[:3] = danger_pos
        reward6, done6, info6 = env._calculate_reward()
        print(f"危险距离时奖励: {reward6:.3f}")
    
    print("\n✅ 奖励函数行为测试完成")

def main():
    """主测试函数"""
    print("🚀 简化奖励函数集成测试")
    print("=" * 60)
    
    # 基础集成测试
    if test_reward_function_integration():
        # 行为特性测试
        test_reward_function_behavior()
        
        print("\n🎯 测试总结:")
        print("✅ 简化奖励函数已成功集成到主文件夹")
        print("✅ 支持简化和复杂两种奖励函数模式")
        print("✅ 环境配置已更新支持奖励类型选择")
        print("✅ 训练函数已支持奖励类型参数")
        print("\n🚀 可以开始使用简化奖励函数进行分阶段训练!")
        
        print("\n📋 下一步建议:")
        print("1. 运行 python simplified_reward_staged_training.py 进行分阶段训练")
        print("2. 运行 python reward_comparison_analysis.py 进行对比实验")
        print("3. 查看 简化奖励函数使用指南.md 了解详细使用方法")
        
    else:
        print("\n❌ 集成测试失败，请检查代码修改")

if __name__ == "__main__":
    main()
