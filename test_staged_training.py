#!/usr/bin/env python3
"""
测试分阶段训练功能
验证staged_training.py是否能正常工作
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_environment_configs():
    """测试环境配置"""
    print("🧪 测试环境配置...")
    
    try:
        from environment_config import get_environment_config, get_training_stage_config
        
        # 测试环境配置
        simple_config = get_environment_config("simple")
        print(f"✅ Simple config: {simple_config}")
        
        complex_static_config = get_environment_config("complex_static")
        print(f"✅ Complex static config: {complex_static_config}")
        
        # 测试训练阶段配置
        stage1_config = get_training_stage_config("stage1_basic")
        print(f"✅ Stage 1 config: {stage1_config}")
        
        return True
    except Exception as e:
        print(f"❌ 环境配置测试失败: {e}")
        return False

def test_train_function():
    """测试训练函数"""
    print("\n🧪 测试训练函数...")
    
    try:
        from train_dwa_rl import train_dwa_rl_model
        
        # 测试函数调用（不实际训练）
        print("✅ train_dwa_rl_model 函数可以导入")
        
        # 测试参数
        import inspect
        sig = inspect.signature(train_dwa_rl_model)
        params = list(sig.parameters.keys())
        print(f"✅ 函数参数: {params}")
        
        if 'environment_config' in params:
            print("✅ environment_config 参数存在")
        else:
            print("❌ environment_config 参数缺失")
            return False
            
        return True
    except Exception as e:
        print(f"❌ 训练函数测试失败: {e}")
        return False

def test_environment_creation():
    """测试环境创建"""
    print("\n🧪 测试环境创建...")
    
    try:
        from dwa_rl_core import StabilizedEnvironment
        
        # 测试简单环境
        env_simple = StabilizedEnvironment(environment_config="simple")
        print(f"✅ 简单环境创建成功，障碍物数量: {len(env_simple.obstacles)}")
        
        # 测试默认环境
        env_default = StabilizedEnvironment()
        print(f"✅ 默认环境创建成功，障碍物数量: {len(env_default.obstacles)}")
        
        return True
    except Exception as e:
        print(f"❌ 环境创建测试失败: {e}")
        return False

def test_staged_training_import():
    """测试分阶段训练导入"""
    print("\n🧪 测试分阶段训练导入...")
    
    try:
        from staged_training import staged_training
        print("✅ staged_training 函数可以导入")
        
        # 检查函数参数
        import inspect
        sig = inspect.signature(staged_training)
        params = list(sig.parameters.keys())
        print(f"✅ 函数参数: {params}")
        
        return True
    except Exception as e:
        print(f"❌ 分阶段训练导入测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 测试 staged_training.py 配套性")
    print("=" * 60)
    
    tests = [
        test_environment_configs,
        test_train_function,
        test_environment_creation,
        test_staged_training_import
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✅ 所有测试通过！staged_training.py 应该能正常工作")
        return True
    else:
        print("❌ 部分测试失败，需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
