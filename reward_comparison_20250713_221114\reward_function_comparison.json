{"experiment_info": {"timestamp": "20250713_221114", "environment": "simple", "episodes": 200, "description": "简化 vs 复杂奖励函数对比"}, "simplified_results": {"reward_type": "simplified", "total_episodes": 200, "success_rate": 0.0, "avg_episode_reward": -362.0788020764934, "std_episode_reward": 46.80909827063914, "reward_cv": 0.12927875921537701, "max_episode_reward": -285.76056708455314, "min_episode_reward": -607.4122202883086, "avg_step_reward": -1.176134225776724, "std_step_reward": 5.85228878765346, "learning_improvement": -0.04780480152506826, "convergence_episode": 40, "final_20_avg": -361.8849370408628}, "complex_results": {"reward_type": "complex", "total_episodes": 200, "success_rate": 1.0, "avg_episode_reward": 2518.6446442695947, "std_episode_reward": 144.97437371701122, "reward_cv": 0.05756047167942332, "max_episode_reward": 3640.3899830269447, "min_episode_reward": 2362.3108959142874, "avg_step_reward": 8.164428812180605, "std_step_reward": 66.54927752522858, "learning_improvement": -0.031903237945200724, "convergence_episode": 40, "final_20_avg": 2541.5637233482958}, "comparison_analysis": {"success_rate_improvement": -1.0, "reward_stability_improvement": -0.07171828753595369, "convergence_speed_improvement": 0, "learning_improvement_diff": -0.015901563579867536, "final_performance_improvement": -2903.4486603891587, "avg_reward_difference": -2880.723446346088}}