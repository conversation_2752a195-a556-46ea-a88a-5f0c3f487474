{"experiment_info": {"timestamp": "20250702_014530", "total_episodes_per_type": 2000, "random_seed": 42, "comparison_type": "deep_training_comparison"}, "complex_reward_results": {"total_episodes": 2000, "success_rate": 0.949, "collision_rate": 0.0, "timeout_rate": 0.0, "avg_episode_reward": 557.272082112668, "std_episode_reward": 53.14679690671574, "reward_cv": 0.09536956652346823, "avg_episode_steps": 318.9125, "std_episode_steps": 33.23861976301062, "total_training_time": 17501.30458331108, "avg_episode_time": 8.750652291655541, "final_50_success_rate": 0.98, "learning_improvement": 0.10278408183779235, "convergence_episode": 40, "best_episode_reward": 605.7783325056556, "worst_episode_reward": 171.69650059237196}, "simplified_reward_results": {"total_episodes": 2000, "success_rate": 0.9475, "collision_rate": 0.0, "timeout_rate": 0.0, "avg_episode_reward": -436.0091994103156, "std_episode_reward": 239.47536959391752, "reward_cv": 0.549243846042235, "avg_episode_steps": 319.3725, "std_episode_steps": 34.73895139105382, "total_training_time": 15418.621197938919, "avg_episode_time": 7.709310598969459, "final_50_success_rate": 0.94, "learning_improvement": 0.024773816223718992, "convergence_episode": 59, "best_episode_reward": -299.1172866107003, "worst_episode_reward": -2140.3727625621054}, "comparative_analysis": {"success_rate_improvement": -0.0014999999999999458, "reward_stability_improvement": -0.45387427951876674, "convergence_speed_improvement": -19, "learning_improvement_diff": -0.07801026561407336, "training_time_difference": 2082.683385372162, "final_performance_improvement": -0.040000000000000036}}