"""
专用训练对比代码
分别在原始复杂奖励函数和论文风格简化奖励函数环境中训练200次
用于深度对比两种奖励函数设计的效果
"""

import numpy as np
import matplotlib.pyplot as plt
import json
import os
import time
import argparse
from datetime import datetime
from collections import deque
import pickle

from dwa_rl_core import StabilizedEnvironment, StabilizedTD3Controller, td3_config

class DedicatedTrainer:
    """专用训练器 - 深度对比两种奖励函数"""
    
    def __init__(self, episodes=2000, seed=42):
        self.episodes = episodes
        self.seed = seed
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建输出目录
        self.output_dir = f'dedicated_comparison_{self.timestamp}'
        os.makedirs(self.output_dir, exist_ok=True)
        
        print(f"🔬 专用训练对比实验")
        print(f"📅 实验时间: {self.timestamp}")
        print(f"🎯 训练Episodes: {episodes}")
        print(f"📁 输出目录: {self.output_dir}")
        print("=" * 60)
    
    def train_single_type(self, reward_type='complex', save_models=True):
        """训练单个奖励函数类型"""
        print(f"\n🚀 开始训练 - {reward_type.upper()} 奖励函数")
        print(f"🎲 随机种子: {self.seed}")
        print("=" * 50)
        
        # 设置随机种子
        np.random.seed(self.seed)
        
        # 创建环境和控制器
        env = StabilizedEnvironment(reward_type=reward_type)
        controller = StabilizedTD3Controller(td3_config)
        
        # 详细训练统计
        training_data = {
            'reward_type': reward_type,
            'episodes': [],
            'episode_rewards': [],
            'episode_steps': [],
            'episode_step_rewards': [],
            'episode_trajectories': [],
            'success_episodes': [],
            'collision_episodes': [],
            'timeout_episodes': [],
            'success_count': 0,
            'collision_count': 0,
            'timeout_count': 0,
            'training_times': [],
            'q_values': [],
            'actor_losses': [],
            'critic_losses': [],
            'action_qualities': []
        }
        
        total_start_time = time.time()
        
        for episode in range(self.episodes):
            episode_start_time = time.time()
            
            # 重置环境
            state = env.reset()
            full_state = np.concatenate([env.state, state[6:]])
            
            episode_reward = 0
            step_count = 0
            episode_step_rewards = []
            trajectory = []
            episode_done = False
            episode_result = 'timeout'
            
            while step_count < 500 and not episode_done:
                # 记录轨迹
                trajectory.append(env.state[:3].copy())
                
                # 获取动作
                action, info, safe_actions = controller.get_action_with_quality(
                    full_state, env.goal, env.obstacles, add_noise=(episode > 10)
                )
                
                # 执行动作
                next_state, reward, done, env_info = env.step(action)
                next_full_state = np.concatenate([env.state, next_state[6:]])
                
                # 记录奖励和质量
                episode_step_rewards.append(reward)
                episode_reward += reward
                step_count += 1
                
                # 记录动作质量
                if 'quality_score' in info:
                    training_data['action_qualities'].append(info['quality_score'])
                
                # 存储经验
                controller.replay_buffer.add(
                    full_state.copy(),
                    action.copy(),
                    reward,
                    next_full_state.copy(),
                    done,
                    safe_actions,
                    env.goal.copy(),
                    [obs.copy() for obs in env.obstacles],
                    info.get('selected_idx', 0)
                )
                
                # 训练更新
                critic_loss, actor_loss = controller.immediate_update(batch_size=64)
                if critic_loss is not None:
                    training_data['critic_losses'].append(critic_loss)
                if actor_loss is not None:
                    training_data['actor_losses'].append(actor_loss)
                
                # 记录Q值
                if len(controller.q_values) > 0:
                    training_data['q_values'].append(controller.q_values[-1])
                
                if done:
                    episode_done = True
                    if env_info.get('success', False):
                        training_data['success_count'] += 1
                        training_data['success_episodes'].append(episode)
                        episode_result = 'success'
                    elif env_info.get('collision', False):
                        training_data['collision_count'] += 1
                        training_data['collision_episodes'].append(episode)
                        episode_result = 'collision'
                    break
                
                full_state = next_full_state
            
            if not episode_done:
                training_data['timeout_count'] += 1
                training_data['timeout_episodes'].append(episode)
                episode_result = 'timeout'
            
            episode_time = time.time() - episode_start_time
            
            # 记录episode数据
            training_data['episodes'].append(episode)
            training_data['episode_rewards'].append(episode_reward)
            training_data['episode_steps'].append(step_count)
            training_data['episode_step_rewards'].append(episode_step_rewards)
            training_data['episode_trajectories'].append(trajectory)
            training_data['training_times'].append(episode_time)
            
            # 每10个episode报告一次
            if (episode + 1) % 10 == 0:
                recent_rewards = training_data['episode_rewards'][-10:]
                avg_reward = np.mean(recent_rewards)
                success_rate = training_data['success_count'] / (episode + 1)
                avg_steps = np.mean(training_data['episode_steps'][-10:])
                
                print(f"Episode {episode+1:3d}: Reward={avg_reward:7.1f}, "
                      f"Success={success_rate:.3f}, Steps={avg_steps:.1f}, Result={episode_result}")
        
        total_training_time = time.time() - total_start_time
        
        # 计算最终统计
        final_stats = self.calculate_final_statistics(training_data, total_training_time)
        
        # 保存训练数据
        if save_models:
            self.save_training_results(reward_type, controller, training_data, final_stats)
        
        print(f"\n📊 {reward_type.upper()} 训练完成!")
        print(f"✅ 成功率: {final_stats['success_rate']:.3f}")
        print(f"📈 平均Episode奖励: {final_stats['avg_episode_reward']:.1f}")
        print(f"📉 奖励稳定性(CV): {final_stats['reward_cv']:.3f}")
        print(f"⏱️ 总训练时间: {final_stats['total_training_time']:.1f}秒")
        
        return training_data, final_stats, controller
    
    def calculate_final_statistics(self, training_data, total_time):
        """计算最终统计指标"""
        episode_rewards = training_data['episode_rewards']
        episode_steps = training_data['episode_steps']
        
        stats = {
            'total_episodes': len(episode_rewards),
            'success_rate': training_data['success_count'] / len(episode_rewards),
            'collision_rate': training_data['collision_count'] / len(episode_rewards),
            'timeout_rate': training_data['timeout_count'] / len(episode_rewards),
            'avg_episode_reward': np.mean(episode_rewards),
            'std_episode_reward': np.std(episode_rewards),
            'reward_cv': np.std(episode_rewards) / abs(np.mean(episode_rewards)) if np.mean(episode_rewards) != 0 else 0,
            'avg_episode_steps': np.mean(episode_steps),
            'std_episode_steps': np.std(episode_steps),
            'total_training_time': total_time,
            'avg_episode_time': total_time / len(episode_rewards),
            'final_50_success_rate': np.sum([1 for i in training_data['success_episodes'] if i >= len(episode_rewards)-50]) / 50,
            'learning_improvement': self.calculate_learning_improvement(episode_rewards),
            'convergence_episode': self.find_convergence_point(episode_rewards),
            'best_episode_reward': np.max(episode_rewards),
            'worst_episode_reward': np.min(episode_rewards)
        }
        
        return stats
    
    def calculate_learning_improvement(self, episode_rewards):
        """计算学习改善程度"""
        if len(episode_rewards) < 20:
            return 0
        
        first_20 = np.mean(episode_rewards[:20])
        last_20 = np.mean(episode_rewards[-20:])
        
        if first_20 != 0:
            return (last_20 - first_20) / abs(first_20)
        else:
            return 0
    
    def find_convergence_point(self, episode_rewards, window=20, threshold=0.05):
        """寻找收敛点"""
        if len(episode_rewards) < window * 2:
            return len(episode_rewards)
        
        for i in range(window, len(episode_rewards) - window):
            current_window = episode_rewards[i:i+window]
            next_window = episode_rewards[i+window:i+2*window]
            
            if len(current_window) == window and len(next_window) == window:
                current_mean = np.mean(current_window)
                next_mean = np.mean(next_window)
                
                if current_mean != 0:
                    relative_change = abs(next_mean - current_mean) / abs(current_mean)
                    if relative_change < threshold:
                        return i + window
        
        return len(episode_rewards)
    
    def save_training_results(self, reward_type, controller, training_data, final_stats):
        """保存训练结果"""
        # 保存模型
        model_path = os.path.join(self.output_dir, f'{reward_type}_model.pth')
        controller.save_model(model_path)
        
        # 保存训练数据
        data_path = os.path.join(self.output_dir, f'{reward_type}_training_data.pkl')
        with open(data_path, 'wb') as f:
            pickle.dump({
                'training_data': training_data,
                'final_stats': final_stats
            }, f)
        
        # 保存JSON报告
        json_path = os.path.join(self.output_dir, f'{reward_type}_training_report.json')
        
        # 准备JSON兼容的数据
        json_data = {
            'experiment_info': {
                'reward_type': reward_type,
                'total_episodes': final_stats['total_episodes'],
                'timestamp': self.timestamp,
                'training_time_seconds': final_stats['total_training_time']
            },
            'performance_metrics': final_stats,
            'episode_rewards': training_data['episode_rewards'],
            'episode_steps': training_data['episode_steps'],
            'success_episodes': training_data['success_episodes'],
            'collision_episodes': training_data['collision_episodes'],
            'timeout_episodes': training_data['timeout_episodes']
        }
        
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)
        
        print(f"💾 {reward_type.upper()} 训练结果已保存:")
        print(f"  • 模型: {model_path}")
        print(f"  • 数据: {data_path}")
        print(f"  • 报告: {json_path}")
    
    def run_full_comparison(self):
        """运行完整的对比实验"""
        print("🎯 开始完整的奖励函数对比实验")
        print(f"📊 总共将训练 {self.episodes * 2} 个episodes")
        print("=" * 60)
        
        # 训练复杂奖励函数
        complex_data, complex_stats, complex_controller = self.train_single_type('complex')
        
        print("\n" + "="*60)
        
        # 训练简化奖励函数
        simple_data, simple_stats, simple_controller = self.train_single_type('simplified')
        
        # 生成对比分析
        self.generate_comparison_analysis(complex_data, complex_stats, simple_data, simple_stats)
        
        return {
            'complex': {'data': complex_data, 'stats': complex_stats, 'controller': complex_controller},
            'simplified': {'data': simple_data, 'stats': simple_stats, 'controller': simple_controller}
        }
    
    def generate_comparison_analysis(self, complex_data, complex_stats, simple_data, simple_stats):
        """生成训练对比分析报告"""
        analysis = {
            'experiment_info': {
                'timestamp': self.timestamp,
                'total_episodes_per_type': self.episodes,
                'random_seed': self.seed,
                'comparison_type': 'deep_training_comparison'
            },
            'complex_reward_results': complex_stats,
            'simplified_reward_results': simple_stats,
            'comparative_analysis': {
                'success_rate_improvement': simple_stats['success_rate'] - complex_stats['success_rate'],
                'reward_stability_improvement': complex_stats['reward_cv'] - simple_stats['reward_cv'],
                'convergence_speed_improvement': complex_stats['convergence_episode'] - simple_stats['convergence_episode'],
                'learning_improvement_diff': simple_stats['learning_improvement'] - complex_stats['learning_improvement'],
                'training_time_difference': complex_stats['total_training_time'] - simple_stats['total_training_time'],
                'final_performance_improvement': simple_stats['final_50_success_rate'] - complex_stats['final_50_success_rate']
            }
        }
        
        # 保存分析报告
        analysis_path = os.path.join(self.output_dir, 'deep_comparison_analysis.json')
        with open(analysis_path, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, indent=2, ensure_ascii=False)
        
        # 打印分析结果
        self.print_comparison_results(analysis)
        
        return analysis
    
    def print_comparison_results(self, analysis):
        """打印对比结果"""
        print(f"\n📋 深度训练对比分析报告")
        print("=" * 80)
        
        complex = analysis['complex_reward_results']
        simple = analysis['simplified_reward_results']
        comp = analysis['comparative_analysis']
        
        print(f"🔧 复杂奖励函数 (训练{self.episodes}次):")
        print(f"  • 成功率: {complex['success_rate']:.3f}")
        print(f"  • 奖励稳定性(CV): {complex['reward_cv']:.3f}")
        print(f"  • 收敛Episode: {complex['convergence_episode']}")
        print(f"  • 学习改善: {complex['learning_improvement']:+.3f}")
        print(f"  • 最终50次成功率: {complex['final_50_success_rate']:.3f}")
        
        print(f"\n⚡ 简化奖励函数 (训练{self.episodes}次):")
        print(f"  • 成功率: {simple['success_rate']:.3f}")
        print(f"  • 奖励稳定性(CV): {simple['reward_cv']:.3f}")
        print(f"  • 收敛Episode: {simple['convergence_episode']}")
        print(f"  • 学习改善: {simple['learning_improvement']:+.3f}")
        print(f"  • 最终50次成功率: {simple['final_50_success_rate']:.3f}")
        
        print(f"\n📊 对比改善:")
        print(f"  • 成功率改善: {comp['success_rate_improvement']:+.3f}")
        print(f"  • 稳定性改善: {comp['reward_stability_improvement']:+.3f}")
        print(f"  • 收敛速度改善: {comp['convergence_speed_improvement']:+.0f} episodes")
        print(f"  • 学习效果改善: {comp['learning_improvement_diff']:+.3f}")
        print(f"  • 最终性能改善: {comp['final_performance_improvement']:+.3f}")

def main():
    parser = argparse.ArgumentParser(description='专用训练对比实验')
    parser.add_argument('--episodes', type=int, default=2000, help='每种奖励函数的训练episodes数量')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    
    args = parser.parse_args()
    
    # 创建训练器
    trainer = DedicatedTrainer(episodes=args.episodes, seed=args.seed)
    
    # 运行完整对比
    results = trainer.run_full_comparison()
    
    print(f"\n🎉 专用训练对比实验完成!")
    print(f"📁 所有结果已保存到: {trainer.output_dir}")
    print(f"📊 下一步可以运行性能测试脚本进行实际性能对比")
    
    return results

if __name__ == "__main__":
    main() 