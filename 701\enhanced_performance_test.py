"""
增强版性能对比测试代码
同时运行两个智能体进行实时对比，包含3D可视化和详细性能分析
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import json
import os
import time
import argparse
from datetime import datetime
import pickle
import glob
from collections import defaultdict
import seaborn as sns

from dwa_rl_core import StabilizedEnvironment, StabilizedTD3Controller, td3_config, load_trained_model

class EnhancedPerformanceComparator:
    """增强版性能对比器 - 同时测试两种模型并实时可视化"""
    
    def __init__(self, model_dir, test_episodes=10):
        self.model_dir = model_dir
        self.test_episodes = test_episodes
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建测试输出目录
        self.output_dir = f'enhanced_performance_test_{self.timestamp}'
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 测试场景配置
        self.test_scenarios = self.get_test_scenarios()
        
        # 数据收集容器
        self.episode_data = defaultdict(list)
        self.comparison_metrics = defaultdict(list)
        
        print(f"🧪 增强版性能对比测试")
        print(f"📅 测试时间: {self.timestamp}")
        print(f"🎯 测试Episodes: {test_episodes}")
        print(f"📁 模型目录: {model_dir}")
        print(f"📁 输出目录: {self.output_dir}")
        print("=" * 60)
    
    def get_test_scenarios(self):
        """获取测试场景"""
        scenarios = [
            {
                'name': 'Simple_Race',
                'description': '简单竞速 - 短距离无障碍物',
                'start': [10, 10, 10],
                'goal': [40, 40, 40],
                'obstacles': []
            },
            {
                'name': 'Standard_Navigation',
                'description': '标准导航 - 中等障碍物',
                'start': [10, 10, 10],
                'goal': [90, 90, 90],
                'obstacles': [
                    {'center': [30, 30, 30], 'radius': 8},
                    {'center': [50, 60, 50], 'radius': 6},
                    {'center': [70, 40, 70], 'radius': 7}
                ]
            },
            {
                'name': 'Dense_Challenge',
                'description': '密集挑战 - 复杂障碍物',
                'start': [5, 5, 5],
                'goal': [95, 95, 95],
                'obstacles': [
                    {'center': [20, 20, 20], 'radius': 5},
                    {'center': [35, 35, 35], 'radius': 6},
                    {'center': [50, 25, 50], 'radius': 4},
                    {'center': [65, 65, 40], 'radius': 5},
                    {'center': [80, 45, 80], 'radius': 7}
                ]
            }
        ]
        return scenarios
    
    def load_models(self):
        """加载训练好的模型"""
        models = {}
        
        # 查找模型文件
        complex_model_path = os.path.join(self.model_dir, 'complex_model.pth')
        simple_model_path = os.path.join(self.model_dir, 'simplified_model.pth')
        
        if os.path.exists(complex_model_path):
            models['complex'] = load_trained_model(complex_model_path, td3_config)
            print(f"✅ 复杂奖励函数模型已加载")
        
        if os.path.exists(simple_model_path):
            models['simplified'] = load_trained_model(simple_model_path, td3_config)
            print(f"✅ 简化奖励函数模型已加载")
        
        if len(models) != 2:
            raise FileNotFoundError(f"需要同时找到两个模型文件进行对比")
        
        return models 

    def run_simultaneous_episode(self, models, scenario, episode_idx):
        """运行一个episode，两个智能体同时测试"""
        print(f"  🏃‍♂️ Episode {episode_idx + 1}: 双智能体同时运行...")
        
        # 创建两个环境实例
        env_complex = StabilizedEnvironment(reward_type='complex')
        env_simplified = StabilizedEnvironment(reward_type='simplified')
        
        # 设置相同的场景
        for env in [env_complex, env_simplified]:
            env.start = np.array(scenario['start'])
            env.goal = np.array(scenario['goal'])
            env.obstacles = scenario['obstacles']
        
        # 初始化环境
        state_complex = env_complex.reset()
        state_simplified = env_simplified.reset()
        
        full_state_complex = np.concatenate([env_complex.state, state_complex[6:]])
        full_state_simplified = np.concatenate([env_simplified.state, state_simplified[6:]])
        
        # Episode数据收集
        episode_data = {
            'episode': episode_idx,
            'scenario': scenario['name'],
            'complex': {
                'trajectory': [],
                'velocities': [],
                'actions': [],
                'rewards': [],
                'safe_action_counts': [],
                'quality_scores': [],
                'energy_consumption': 0,
                'completion_time': 0,
                'total_steps': 0,
                'result': 'timeout',
                'safety_margins': []
            },
            'simplified': {
                'trajectory': [],
                'velocities': [],
                'actions': [],
                'rewards': [],
                'safe_action_counts': [],
                'quality_scores': [],
                'energy_consumption': 0,
                'completion_time': 0,
                'total_steps': 0,
                'result': 'timeout',
                'safety_margins': []
            }
        }
        
        start_time = time.time()
        step_count = 0
        max_steps = 800
        
        # 同时运行两个智能体
        while step_count < max_steps:
            # 记录当前位置
            pos_complex = env_complex.state[:3].copy()
            pos_simplified = env_simplified.state[:3].copy()
            
            episode_data['complex']['trajectory'].append(pos_complex)
            episode_data['simplified']['trajectory'].append(pos_simplified)
            
            # 获取动作 - Complex模型
            try:
                action_complex, info_complex, safe_actions_complex = models['complex'].get_action_with_quality(
                    full_state_complex, env_complex.goal, env_complex.obstacles, add_noise=True
                )
                
                episode_data['complex']['actions'].append(action_complex.copy())
                episode_data['complex']['safe_action_counts'].append(info_complex.get('num_safe_actions', 0))
                episode_data['complex']['quality_scores'].append(info_complex.get('quality_score', 0))
                episode_data['complex']['velocities'].append(np.linalg.norm(env_complex.state[3:6]))
                
            except Exception as e:
                print(f"    ⚠️ Complex模型动作获取失败: {e}")
                break
            
            # 获取动作 - Simplified模型
            try:
                action_simplified, info_simplified, safe_actions_simplified = models['simplified'].get_action_with_quality(
                    full_state_simplified, env_simplified.goal, env_simplified.obstacles, add_noise=True
                )
                
                episode_data['simplified']['actions'].append(action_simplified.copy())
                episode_data['simplified']['safe_action_counts'].append(info_simplified.get('num_safe_actions', 0))
                episode_data['simplified']['quality_scores'].append(info_simplified.get('quality_score', 0))
                episode_data['simplified']['velocities'].append(np.linalg.norm(env_simplified.state[3:6]))
                
            except Exception as e:
                print(f"    ⚠️ Simplified模型动作获取失败: {e}")
                break
            
            # 执行动作
            try:
                next_state_complex, reward_complex, done_complex, info_env_complex = env_complex.step(action_complex)
                next_state_simplified, reward_simplified, done_simplified, info_env_simplified = env_simplified.step(action_simplified)
                
                episode_data['complex']['rewards'].append(reward_complex)
                episode_data['simplified']['rewards'].append(reward_simplified)
                
                full_state_complex = np.concatenate([env_complex.state, next_state_complex[6:]])
                full_state_simplified = np.concatenate([env_simplified.state, next_state_simplified[6:]])
                
            except Exception as e:
                print(f"    ⚠️ 环境步进失败: {e}")
                break
            
            # 计算安全边距
            for model_data, env, pos in [
                (episode_data['complex'], env_complex, pos_complex),
                (episode_data['simplified'], env_simplified, pos_simplified)
            ]:
                min_obstacle_dist = float('inf')
                if env.obstacles:
                    for obs in env.obstacles:
                        dist = np.linalg.norm(pos - obs['center']) - obs['radius']
                        min_obstacle_dist = min(min_obstacle_dist, dist)
                model_data['safety_margins'].append(min_obstacle_dist)
            
            step_count += 1
            
            # 检查是否到达目标
            goal_dist_complex = np.linalg.norm(pos_complex - env_complex.goal)
            goal_dist_simplified = np.linalg.norm(pos_simplified - env_simplified.goal)
            
            if goal_dist_complex < 5.0:
                episode_data['complex']['result'] = 'success'
                episode_data['complex']['completion_time'] = time.time() - start_time
                episode_data['complex']['total_steps'] = step_count
                print(f"    🎉 Complex模型成功到达目标！步数: {step_count}")
            
            if goal_dist_simplified < 5.0:
                episode_data['simplified']['result'] = 'success'
                episode_data['simplified']['completion_time'] = time.time() - start_time
                episode_data['simplified']['total_steps'] = step_count
                print(f"    🎉 Simplified模型成功到达目标！步数: {step_count}")
            
            # 检查环境返回的完成状态
            if done_complex and info_env_complex.get('success', False):
                episode_data['complex']['result'] = 'success'
                episode_data['complex']['completion_time'] = time.time() - start_time
                episode_data['complex']['total_steps'] = step_count
                
            if done_simplified and info_env_simplified.get('success', False):
                episode_data['simplified']['result'] = 'success'
                episode_data['simplified']['completion_time'] = time.time() - start_time
                episode_data['simplified']['total_steps'] = step_count
            
            # 如果都完成了就结束
            if (episode_data['complex']['result'] == 'success' and 
                episode_data['simplified']['result'] == 'success'):
                break
        
        # 计算最终数据
        total_time = time.time() - start_time
        
        for model_name, model_data in [('complex', episode_data['complex']), 
                                       ('simplified', episode_data['simplified'])]:
            if model_data['result'] == 'timeout':
                model_data['completion_time'] = total_time
                model_data['total_steps'] = step_count
            
            # 计算路径长度
            trajectory = np.array(model_data['trajectory'])
            if len(trajectory) > 1:
                path_segments = np.diff(trajectory, axis=0)
                model_data['path_length'] = np.sum(np.linalg.norm(path_segments, axis=1))
                
                # 路径效率
                straight_distance = np.linalg.norm(trajectory[-1] - trajectory[0])
                model_data['efficiency'] = straight_distance / model_data['path_length'] if model_data['path_length'] > 0 else 0
                
                # 路径平滑度 (曲率变化)
                if len(path_segments) > 1:
                    direction_changes = []
                    for i in range(1, len(path_segments)):
                        if np.linalg.norm(path_segments[i-1]) > 0 and np.linalg.norm(path_segments[i]) > 0:
                            cos_angle = np.dot(path_segments[i-1], path_segments[i]) / (
                                np.linalg.norm(path_segments[i-1]) * np.linalg.norm(path_segments[i])
                            )
                            direction_changes.append(np.arccos(np.clip(cos_angle, -1, 1)))
                    model_data['path_smoothness'] = np.mean(direction_changes) if direction_changes else 0
                else:
                    model_data['path_smoothness'] = 0
            else:
                model_data['path_length'] = 0
                model_data['efficiency'] = 0
                model_data['path_smoothness'] = 0
            
            # 能量消耗 (基于动作强度)
            if model_data['actions']:
                action_magnitudes = [np.linalg.norm(action) for action in model_data['actions']]
                model_data['energy_consumption'] = np.sum(action_magnitudes)
                model_data['avg_velocity'] = np.mean(model_data['velocities']) if model_data['velocities'] else 0
                model_data['avg_quality_score'] = np.mean(model_data['quality_scores']) if model_data['quality_scores'] else 0
                model_data['avg_safe_actions'] = np.mean(model_data['safe_action_counts']) if model_data['safe_action_counts'] else 0
                model_data['min_safety_margin'] = min(model_data['safety_margins']) if model_data['safety_margins'] else float('inf')
            else:
                model_data['energy_consumption'] = 0
                model_data['avg_velocity'] = 0
                model_data['avg_quality_score'] = 0
                model_data['avg_safe_actions'] = 0
                model_data['min_safety_margin'] = float('inf')
        
        print(f"    📊 结果: Complex={episode_data['complex']['result']}, Simplified={episode_data['simplified']['result']}")
        print(f"    📏 路径长度: Complex={episode_data['complex']['path_length']:.1f}m, Simplified={episode_data['simplified']['path_length']:.1f}m")
        
        return episode_data 
    
    def visualize_episode_3d(self, episode_data, scenario, save_path=None):
        """3D可视化单个episode的对比"""
        fig = plt.figure(figsize=(16, 12))
        ax = fig.add_subplot(111, projection='3d')
        
        # 设置环境
        start = scenario['start']
        goal = scenario['goal']
        
        # 绘制起点和终点
        ax.scatter(start[0], start[1], start[2], c='green', s=300, marker='o', label='起点', alpha=0.9)
        ax.scatter(goal[0], goal[1], goal[2], c='red', s=400, marker='*', label='目标', alpha=0.9)
        
        # 绘制障碍物
        for i, obs in enumerate(scenario['obstacles']):
            center = obs['center']
            radius = obs['radius']
            u = np.linspace(0, 2 * np.pi, 20)
            v = np.linspace(0, np.pi, 20)
            x = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
            y = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
            z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]
            ax.plot_surface(x, y, z, alpha=0.3, color='gray')
        
        # 绘制轨迹
        trajectory_complex = np.array(episode_data['complex']['trajectory'])
        trajectory_simplified = np.array(episode_data['simplified']['trajectory'])
        
        if len(trajectory_complex) > 1:
            # 复杂模型轨迹
            line_style = '-' if episode_data['complex']['result'] == 'success' else '--'
            ax.plot(trajectory_complex[:, 0], trajectory_complex[:, 1], trajectory_complex[:, 2],
                   color='blue', linewidth=4, alpha=0.8, linestyle=line_style,
                   label=f'Complex模型 ({episode_data["complex"]["result"]})')
            
            # 标记起始点
            ax.scatter(trajectory_complex[0, 0], trajectory_complex[0, 1], trajectory_complex[0, 2],
                      color='blue', s=100, marker='o', alpha=0.8)
        
        if len(trajectory_simplified) > 1:
            # 简化模型轨迹
            line_style = '-' if episode_data['simplified']['result'] == 'success' else '--'
            ax.plot(trajectory_simplified[:, 0], trajectory_simplified[:, 1], trajectory_simplified[:, 2],
                   color='orange', linewidth=4, alpha=0.8, linestyle=line_style,
                   label=f'Simplified模型 ({episode_data["simplified"]["result"]})')
            
            # 标记起始点
            ax.scatter(trajectory_simplified[0, 0], trajectory_simplified[0, 1], trajectory_simplified[0, 2],
                      color='orange', s=100, marker='o', alpha=0.8)
        
        # 设置图表
        ax.set_xlabel('X (m)', fontsize=12)
        ax.set_ylabel('Y (m)', fontsize=12)
        ax.set_zlabel('Z (m)', fontsize=12)
        
        # 性能对比信息
        title_text = f'Episode {episode_data["episode"] + 1} - {scenario["name"]}\n'
        title_text += f'路径长度: Complex={episode_data["complex"]["path_length"]:.1f}m, Simplified={episode_data["simplified"]["path_length"]:.1f}m\n'
        title_text += f'完成时间: Complex={episode_data["complex"]["completion_time"]:.1f}s, Simplified={episode_data["simplified"]["completion_time"]:.1f}s'
        
        ax.set_title(title_text, fontsize=14, fontweight='bold')
        ax.legend(fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # 设置坐标轴范围
        all_points = []
        if len(trajectory_complex) > 1:
            all_points.extend(trajectory_complex)
        if len(trajectory_simplified) > 1:
            all_points.extend(trajectory_simplified)
        
        if all_points:
            all_points = np.array(all_points)
            margin = 15
            ax.set_xlim(max(0, all_points[:, 0].min() - margin), all_points[:, 0].max() + margin)
            ax.set_ylim(max(0, all_points[:, 1].min() - margin), all_points[:, 1].max() + margin)
            ax.set_zlim(max(0, all_points[:, 2].min() - margin), all_points[:, 2].max() + margin)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            print(f"    💾 3D轨迹图已保存: {save_path}")
        else:
            plt.show()
    
    def generate_comparison_charts(self, all_episode_data):
        """生成详细的对比图表"""
        print("\n📊 生成对比分析图表...")
        
        # 数据整理
        metrics_data = {
            'path_length': {'complex': [], 'simplified': []},
            'completion_time': {'complex': [], 'simplified': []},
            'energy_consumption': {'complex': [], 'simplified': []},
            'efficiency': {'complex': [], 'simplified': []},
            'path_smoothness': {'complex': [], 'simplified': []},
            'avg_velocity': {'complex': [], 'simplified': []},
            'avg_quality_score': {'complex': [], 'simplified': []},
            'min_safety_margin': {'complex': [], 'simplified': []},
            'success_rate': {'complex': 0, 'simplified': 0}
        }
        
        episode_numbers = []
        scenario_names = []
        
        for episode_data in all_episode_data:
            episode_numbers.append(episode_data['episode'])
            scenario_names.append(episode_data['scenario'])
            
            for model in ['complex', 'simplified']:
                data = episode_data[model]
                metrics_data['path_length'][model].append(data['path_length'])
                metrics_data['completion_time'][model].append(data['completion_time'])
                metrics_data['energy_consumption'][model].append(data['energy_consumption'])
                metrics_data['efficiency'][model].append(data['efficiency'])
                metrics_data['path_smoothness'][model].append(data['path_smoothness'])
                metrics_data['avg_velocity'][model].append(data['avg_velocity'])
                metrics_data['avg_quality_score'][model].append(data['avg_quality_score'])
                metrics_data['min_safety_margin'][model].append(data['min_safety_margin'])
                
                if data['result'] == 'success':
                    metrics_data['success_rate'][model] += 1
        
        # 计算成功率
        total_episodes = len(all_episode_data)
        metrics_data['success_rate']['complex'] /= total_episodes
        metrics_data['success_rate']['simplified'] /= total_episodes
        
        # 创建多子图对比
        fig, axes = plt.subplots(3, 3, figsize=(20, 15))
        fig.suptitle('DWA-RL性能对比分析', fontsize=16, fontweight='bold')
        
        # 1. 路径长度对比
        ax = axes[0, 0]
        ax.plot(episode_numbers, metrics_data['path_length']['complex'], 'b-o', label='Complex', alpha=0.7)
        ax.plot(episode_numbers, metrics_data['path_length']['simplified'], 'orange', marker='s', label='Simplified', alpha=0.7)
        ax.set_title('路径长度对比')
        ax.set_xlabel('Episode')
        ax.set_ylabel('路径长度 (m)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 2. 完成时间对比
        ax = axes[0, 1]
        ax.plot(episode_numbers, metrics_data['completion_time']['complex'], 'b-o', label='Complex', alpha=0.7)
        ax.plot(episode_numbers, metrics_data['completion_time']['simplified'], 'orange', marker='s', label='Simplified', alpha=0.7)
        ax.set_title('完成时间对比')
        ax.set_xlabel('Episode')
        ax.set_ylabel('完成时间 (s)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 3. 能量消耗对比
        ax = axes[0, 2]
        ax.plot(episode_numbers, metrics_data['energy_consumption']['complex'], 'b-o', label='Complex', alpha=0.7)
        ax.plot(episode_numbers, metrics_data['energy_consumption']['simplified'], 'orange', marker='s', label='Simplified', alpha=0.7)
        ax.set_title('能量消耗对比')
        ax.set_xlabel('Episode')
        ax.set_ylabel('能量消耗')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 4. 路径效率对比
        ax = axes[1, 0]
        ax.plot(episode_numbers, metrics_data['efficiency']['complex'], 'b-o', label='Complex', alpha=0.7)
        ax.plot(episode_numbers, metrics_data['efficiency']['simplified'], 'orange', marker='s', label='Simplified', alpha=0.7)
        ax.set_title('路径效率对比')
        ax.set_xlabel('Episode')
        ax.set_ylabel('路径效率')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 5. 平均速度对比
        ax = axes[1, 1]
        ax.plot(episode_numbers, metrics_data['avg_velocity']['complex'], 'b-o', label='Complex', alpha=0.7)
        ax.plot(episode_numbers, metrics_data['avg_velocity']['simplified'], 'orange', marker='s', label='Simplified', alpha=0.7)
        ax.set_title('平均速度对比')
        ax.set_xlabel('Episode')
        ax.set_ylabel('平均速度 (m/s)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 6. 决策质量对比
        ax = axes[1, 2]
        ax.plot(episode_numbers, metrics_data['avg_quality_score']['complex'], 'b-o', label='Complex', alpha=0.7)
        ax.plot(episode_numbers, metrics_data['avg_quality_score']['simplified'], 'orange', marker='s', label='Simplified', alpha=0.7)
        ax.set_title('决策质量对比')
        ax.set_xlabel('Episode')
        ax.set_ylabel('平均质量分数')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 7. 成功率对比 (柱状图)
        ax = axes[2, 0]
        models = ['Complex', 'Simplified']
        success_rates = [metrics_data['success_rate']['complex'], metrics_data['success_rate']['simplified']]
        colors = ['blue', 'orange']
        bars = ax.bar(models, success_rates, color=colors, alpha=0.7)
        ax.set_title('成功率对比')
        ax.set_ylabel('成功率')
        ax.set_ylim(0, 1.1)
        
        # 在柱状图上添加数值
        for bar, rate in zip(bars, success_rates):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                   f'{rate:.1%}', ha='center', va='bottom', fontweight='bold')
        
        # 8. 综合性能雷达图
        ax = axes[2, 1]
        categories = ['路径效率', '成功率', '速度', '质量分数', '安全边距']
        
        # 标准化数据到0-1范围
        complex_values = [
            np.mean(metrics_data['efficiency']['complex']),
            metrics_data['success_rate']['complex'],
            np.mean(metrics_data['avg_velocity']['complex']) / 5.0,  # 假设最大速度5m/s
            np.mean(metrics_data['avg_quality_score']['complex']),
            min(1.0, np.mean([m for m in metrics_data['min_safety_margin']['complex'] if m != float('inf')]) / 10.0)
        ]
        
        simplified_values = [
            np.mean(metrics_data['efficiency']['simplified']),
            metrics_data['success_rate']['simplified'],
            np.mean(metrics_data['avg_velocity']['simplified']) / 5.0,
            np.mean(metrics_data['avg_quality_score']['simplified']),
            min(1.0, np.mean([m for m in metrics_data['min_safety_margin']['simplified'] if m != float('inf')]) / 10.0)
        ]
        
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        complex_values += complex_values[:1]  # 闭合图形
        simplified_values += simplified_values[:1]
        angles += angles[:1]
        
        ax.plot(angles, complex_values, 'b-', linewidth=2, label='Complex', alpha=0.7)
        ax.fill(angles, complex_values, 'blue', alpha=0.25)
        ax.plot(angles, simplified_values, 'orange', linewidth=2, label='Simplified', alpha=0.7)
        ax.fill(angles, simplified_values, 'orange', alpha=0.25)
        
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(categories)
        ax.set_ylim(0, 1)
        ax.set_title('综合性能雷达图')
        ax.legend()
        ax.grid(True)
        
        # 9. 箱线图对比
        ax = axes[2, 2]
        path_length_data = [metrics_data['path_length']['complex'], metrics_data['path_length']['simplified']]
        bp = ax.boxplot(path_length_data, labels=['Complex', 'Simplified'], patch_artist=True)
        bp['boxes'][0].set_facecolor('blue')
        bp['boxes'][1].set_facecolor('orange')
        bp['boxes'][0].set_alpha(0.7)
        bp['boxes'][1].set_alpha(0.7)
        ax.set_title('路径长度分布对比')
        ax.set_ylabel('路径长度 (m)')
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        chart_path = os.path.join(self.output_dir, 'comprehensive_comparison_charts.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        print(f"✅ 对比图表已保存: {chart_path}")
        
        return metrics_data
    
    def run_full_comparison_test(self):
        """运行完整的对比测试"""
        print("🚀 开始增强版性能对比测试")
        
        # 加载模型
        models = self.load_models()
        
        all_episode_data = []
        
        # 对每个场景进行测试
        for scenario in self.test_scenarios:
            print(f"\n🗺️ 测试场景: {scenario['name']}")
            
            scenario_episodes = []
            
            for episode in range(self.test_episodes):
                # 运行同时对比测试
                episode_data = self.run_simultaneous_episode(models, scenario, episode)
                scenario_episodes.append(episode_data)
                all_episode_data.append(episode_data)
                
                # 生成3D可视化 (每隔几个episode保存一次)
                if episode % max(1, self.test_episodes // 3) == 0:
                    viz_path = os.path.join(self.output_dir, 
                                          f'{scenario["name"]}_episode_{episode + 1}_3d.png')
                    self.visualize_episode_3d(episode_data, scenario, viz_path)
        
        # 生成综合分析报告
        metrics_data = self.generate_comparison_charts(all_episode_data)
        
        # 保存所有数据
        self.save_comprehensive_results(all_episode_data, metrics_data)
        
        return all_episode_data, metrics_data
    
    def save_comprehensive_results(self, all_episode_data, metrics_data):
        """保存综合测试结果"""
        results = {
            'experiment_info': {
                'timestamp': self.timestamp,
                'test_episodes_per_scenario': self.test_episodes,
                'total_episodes': len(all_episode_data),
                'scenarios': [scenario['name'] for scenario in self.test_scenarios]
            },
            'episode_data': all_episode_data,
            'metrics_summary': metrics_data,
            'performance_comparison': {
                'complex_model': {
                    'avg_path_length': np.mean(metrics_data['path_length']['complex']),
                    'avg_completion_time': np.mean(metrics_data['completion_time']['complex']),
                    'avg_energy_consumption': np.mean(metrics_data['energy_consumption']['complex']),
                    'avg_efficiency': np.mean(metrics_data['efficiency']['complex']),
                    'success_rate': metrics_data['success_rate']['complex']
                },
                'simplified_model': {
                    'avg_path_length': np.mean(metrics_data['path_length']['simplified']),
                    'avg_completion_time': np.mean(metrics_data['completion_time']['simplified']),
                    'avg_energy_consumption': np.mean(metrics_data['energy_consumption']['simplified']),
                    'avg_efficiency': np.mean(metrics_data['efficiency']['simplified']),
                    'success_rate': metrics_data['success_rate']['simplified']
                }
            }
        }
        
        # 保存JSON结果
        json_path = os.path.join(self.output_dir, 'enhanced_performance_results.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 完整测试结果已保存: {json_path}")
        print(f"📁 所有输出文件保存在: {self.output_dir}")

def find_latest_training_dir():
    """查找最新的训练结果目录"""
    pattern = 'dedicated_comparison_*'
    dirs = glob.glob(pattern)
    if dirs:
        return max(dirs, key=os.path.getctime)
    return None

def main():
    parser = argparse.ArgumentParser(description='增强版性能对比测试')
    parser.add_argument('--model-dir', type=str, help='包含训练模型的目录')
    parser.add_argument('--test-episodes', type=int, default=8, help='每个场景的测试episodes数量')
    
    args = parser.parse_args()
    
    # 如果没有指定模型目录，尝试自动查找
    model_dir = args.model_dir
    if not model_dir:
        model_dir = find_latest_training_dir()
        if not model_dir:
            print("❌ 找不到训练结果目录，请使用 --model-dir 指定")
            return
        print(f"🔍 自动发现训练结果目录: {model_dir}")
    
    # 创建增强版性能对比器
    comparator = EnhancedPerformanceComparator(
        model_dir=model_dir,
        test_episodes=args.test_episodes
    )
    
    try:
        # 运行增强版性能测试
        all_episode_data, metrics_data = comparator.run_full_comparison_test()
        
        print(f"\n🎉 增强版性能对比测试完成!")
        print(f"📊 测试了 {len(all_episode_data)} 个episodes")
        print(f"📁 所有结果已保存到: {comparator.output_dir}")
        
        # 打印总结
        print(f"\n📈 性能总结:")
        complex_success = metrics_data['success_rate']['complex']
        simplified_success = metrics_data['success_rate']['simplified']
        print(f"🎯 成功率: Complex={complex_success:.1%}, Simplified={simplified_success:.1%}")
        
        complex_avg_path = np.mean(metrics_data['path_length']['complex'])
        simplified_avg_path = np.mean(metrics_data['path_length']['simplified'])
        print(f"📏 平均路径长度: Complex={complex_avg_path:.1f}m, Simplified={simplified_avg_path:.1f}m")
        
        return all_episode_data, metrics_data
    
    except Exception as e:
        print(f"\n❌ 增强版性能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    main()