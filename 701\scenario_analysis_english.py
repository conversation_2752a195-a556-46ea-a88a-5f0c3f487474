"""
Detailed DWA-RL Performance Comparison Analysis by Scenario
Generate detailed analysis charts with 9 subplots for each scenario (English Version)
"""

import numpy as np
import matplotlib.pyplot as plt
import json
import os
from collections import defaultdict
import seaborn as sns

# Set English style
sns.set_style("whitegrid")
plt.rcParams['font.size'] = 10

def load_test_results(results_path):
    """Load test results data"""
    with open(results_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def organize_data_by_scenario(episode_data):
    """Organize data by scenario"""
    scenario_data = defaultdict(lambda: {
        'complex': {
            'episodes': [],
            'success': [],
            'path_length': [],
            'completion_time': [],
            'energy_consumption': [],
            'efficiency': [],
            'avg_velocity': [],
            'avg_quality_score': [],
            'min_safety_margin': []
        },
        'simplified': {
            'episodes': [],
            'success': [],
            'path_length': [],
            'completion_time': [],
            'energy_consumption': [],
            'efficiency': [],
            'avg_velocity': [],
            'avg_quality_score': [],
            'min_safety_margin': []
        }
    })
    
    # Group data by scenario
    for episode in episode_data:
        scenario_name = episode['scenario']
        episode_num = episode['episode']
        
        for model in ['complex', 'simplified']:
            data = episode[model]
            
            scenario_data[scenario_name][model]['episodes'].append(episode_num)
            scenario_data[scenario_name][model]['success'].append(1 if data['result'] == 'success' else 0)
            scenario_data[scenario_name][model]['path_length'].append(data['path_length'])
            scenario_data[scenario_name][model]['completion_time'].append(data['completion_time'])
            scenario_data[scenario_name][model]['energy_consumption'].append(data['energy_consumption'])
            scenario_data[scenario_name][model]['efficiency'].append(data['efficiency'])
            scenario_data[scenario_name][model]['avg_velocity'].append(data['avg_velocity'])
            scenario_data[scenario_name][model]['avg_quality_score'].append(data['avg_quality_score'])
            
            # Handle infinite safety margin values
            safety_margin = data['min_safety_margin']
            if safety_margin == float('inf'):
                safety_margin = 50.0  # Set reasonable upper limit
            scenario_data[scenario_name][model]['min_safety_margin'].append(safety_margin)
    
    return scenario_data

def calculate_cumulative_success_rate(success_list):
    """Calculate cumulative success rate"""
    cumulative_success = []
    total_episodes = 0
    total_success = 0
    
    for success in success_list:
        total_episodes += 1
        total_success += success
        cumulative_success.append(total_success / total_episodes)
    
    return cumulative_success

def generate_scenario_analysis(scenario_name, scenario_data, save_dir):
    """Generate detailed 9-subplot analysis for a single scenario"""
    print(f"📊 Generating detailed analysis for scenario {scenario_name}...")
    
    complex_data = scenario_data['complex']
    simplified_data = scenario_data['simplified']
    
    # Create 3x3 subplots
    fig, axes = plt.subplots(3, 3, figsize=(20, 16))
    fig.suptitle(f'Scenario {scenario_name} - Detailed Performance Comparison Analysis', 
                fontsize=18, fontweight='bold', y=0.95)
    
    # Color and style settings
    complex_color = '#1f77b4'  # Blue
    simplified_color = '#ff7f0e'  # Orange
    
    # 1. Cumulative Success Rate Comparison
    ax = axes[0, 0]
    complex_success_rate = calculate_cumulative_success_rate(complex_data['success'])
    simplified_success_rate = calculate_cumulative_success_rate(simplified_data['success'])
    
    ax.plot(range(1, len(complex_success_rate) + 1), complex_success_rate, 
           color=complex_color, linewidth=3, marker='o', markersize=6, label='Complex Reward', alpha=0.8)
    ax.plot(range(1, len(simplified_success_rate) + 1), simplified_success_rate, 
           color=simplified_color, linewidth=3, marker='s', markersize=6, label='Simplified Reward', alpha=0.8)
    
    ax.set_title('Cumulative Success Rate', fontsize=14, fontweight='bold')
    ax.set_xlabel('Episode')
    ax.set_ylabel('Cumulative Success Rate')
    ax.set_ylim(0, 1.05)
    ax.legend(fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # Add final success rate text
    final_complex = complex_success_rate[-1] if complex_success_rate else 0
    final_simplified = simplified_success_rate[-1] if simplified_success_rate else 0
    ax.text(0.02, 0.98, f'Final Success Rate:\nComplex: {final_complex:.1%}\nSimplified: {final_simplified:.1%}', 
           transform=ax.transAxes, verticalalignment='top', fontsize=10,
           bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 2. Path Length Comparison
    ax = axes[0, 1]
    ax.plot(range(1, len(complex_data['path_length']) + 1), complex_data['path_length'], 
           color=complex_color, linewidth=2, marker='o', markersize=5, label='Complex Reward', alpha=0.7)
    ax.plot(range(1, len(simplified_data['path_length']) + 1), simplified_data['path_length'], 
           color=simplified_color, linewidth=2, marker='s', markersize=5, label='Simplified Reward', alpha=0.7)
    
    ax.set_title('Path Length Comparison', fontsize=14, fontweight='bold')
    ax.set_xlabel('Episode')
    ax.set_ylabel('Path Length (m)')
    ax.legend(fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # Add average lines
    complex_avg = np.mean(complex_data['path_length'])
    simplified_avg = np.mean(simplified_data['path_length'])
    ax.axhline(y=complex_avg, color=complex_color, linestyle='--', alpha=0.6)
    ax.axhline(y=simplified_avg, color=simplified_color, linestyle='--', alpha=0.6)
    
    # 3. Completion Time Comparison
    ax = axes[0, 2]
    ax.plot(range(1, len(complex_data['completion_time']) + 1), complex_data['completion_time'], 
           color=complex_color, linewidth=2, marker='o', markersize=5, label='Complex Reward', alpha=0.7)
    ax.plot(range(1, len(simplified_data['completion_time']) + 1), simplified_data['completion_time'], 
           color=simplified_color, linewidth=2, marker='s', markersize=5, label='Simplified Reward', alpha=0.7)
    
    ax.set_title('Completion Time Comparison', fontsize=14, fontweight='bold')
    ax.set_xlabel('Episode')
    ax.set_ylabel('Completion Time (s)')
    ax.legend(fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 4. Energy Consumption Comparison
    ax = axes[1, 0]
    ax.plot(range(1, len(complex_data['energy_consumption']) + 1), complex_data['energy_consumption'], 
           color=complex_color, linewidth=2, marker='o', markersize=5, label='Complex Reward', alpha=0.7)
    ax.plot(range(1, len(simplified_data['energy_consumption']) + 1), simplified_data['energy_consumption'], 
           color=simplified_color, linewidth=2, marker='s', markersize=5, label='Simplified Reward', alpha=0.7)
    
    ax.set_title('Energy Consumption Comparison', fontsize=14, fontweight='bold')
    ax.set_xlabel('Episode')
    ax.set_ylabel('Energy Consumption')
    ax.legend(fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 5. Path Efficiency Comparison
    ax = axes[1, 1]
    ax.plot(range(1, len(complex_data['efficiency']) + 1), complex_data['efficiency'], 
           color=complex_color, linewidth=2, marker='o', markersize=5, label='Complex Reward', alpha=0.7)
    ax.plot(range(1, len(simplified_data['efficiency']) + 1), simplified_data['efficiency'], 
           color=simplified_color, linewidth=2, marker='s', markersize=5, label='Simplified Reward', alpha=0.7)
    
    ax.set_title('Path Efficiency Comparison', fontsize=14, fontweight='bold')
    ax.set_xlabel('Episode')
    ax.set_ylabel('Path Efficiency')
    ax.set_ylim(0, 1.05)
    ax.legend(fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 6. Average Velocity Comparison
    ax = axes[1, 2]
    ax.plot(range(1, len(complex_data['avg_velocity']) + 1), complex_data['avg_velocity'], 
           color=complex_color, linewidth=2, marker='o', markersize=5, label='Complex Reward', alpha=0.7)
    ax.plot(range(1, len(simplified_data['avg_velocity']) + 1), simplified_data['avg_velocity'], 
           color=simplified_color, linewidth=2, marker='s', markersize=5, label='Simplified Reward', alpha=0.7)
    
    ax.set_title('Average Velocity Comparison', fontsize=14, fontweight='bold')
    ax.set_xlabel('Episode')
    ax.set_ylabel('Average Velocity (m/s)')
    ax.legend(fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 7. Decision Quality Comparison
    ax = axes[2, 0]
    ax.plot(range(1, len(complex_data['avg_quality_score']) + 1), complex_data['avg_quality_score'], 
           color=complex_color, linewidth=2, marker='o', markersize=5, label='Complex Reward', alpha=0.7)
    ax.plot(range(1, len(simplified_data['avg_quality_score']) + 1), simplified_data['avg_quality_score'], 
           color=simplified_color, linewidth=2, marker='s', markersize=5, label='Simplified Reward', alpha=0.7)
    
    ax.set_title('Decision Quality Comparison', fontsize=14, fontweight='bold')
    ax.set_xlabel('Episode')
    ax.set_ylabel('Average Quality Score')
    ax.set_ylim(0, 1.05)
    ax.legend(fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 8. Safety Margin Comparison
    ax = axes[2, 1]
    ax.plot(range(1, len(complex_data['min_safety_margin']) + 1), complex_data['min_safety_margin'], 
           color=complex_color, linewidth=2, marker='o', markersize=5, label='Complex Reward', alpha=0.7)
    ax.plot(range(1, len(simplified_data['min_safety_margin']) + 1), simplified_data['min_safety_margin'], 
           color=simplified_color, linewidth=2, marker='s', markersize=5, label='Simplified Reward', alpha=0.7)
    
    ax.set_title('Minimum Safety Margin Comparison', fontsize=14, fontweight='bold')
    ax.set_xlabel('Episode')
    ax.set_ylabel('Min Safety Margin (m)')
    ax.legend(fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 9. Comprehensive Performance Box Plot Comparison
    ax = axes[2, 2]
    
    # Prepare box plot data - normalized to 0-1 range
    complex_metrics = []
    simplified_metrics = []
    metric_names = ['Success\nRate', 'Path\nEfficiency', 'Velocity\n(norm)', 'Quality\nScore', 'Safety\n(norm)']
    
    # Normalization function
    def normalize(data, min_val=0, max_val=None):
        if max_val is None:
            max_val = max(max(data), 1.0)
        return [(x - min_val) / (max_val - min_val) if max_val > min_val else 0 for x in data]
    
    # Collect data
    complex_metrics.append(complex_data['success'])
    complex_metrics.append(complex_data['efficiency'])
    complex_metrics.append(normalize(complex_data['avg_velocity'], 0, 5.0))  # Assume max velocity 5m/s
    complex_metrics.append(complex_data['avg_quality_score'])
    complex_metrics.append(normalize(complex_data['min_safety_margin'], 0, 20.0))  # Assume max safety margin 20m
    
    simplified_metrics.append(simplified_data['success'])
    simplified_metrics.append(simplified_data['efficiency'])
    simplified_metrics.append(normalize(simplified_data['avg_velocity'], 0, 5.0))
    simplified_metrics.append(simplified_data['avg_quality_score'])
    simplified_metrics.append(normalize(simplified_data['min_safety_margin'], 0, 20.0))
    
    # Draw box plots
    positions_complex = [i - 0.2 for i in range(len(metric_names))]
    positions_simplified = [i + 0.2 for i in range(len(metric_names))]
    
    bp1 = ax.boxplot(complex_metrics, positions=positions_complex, widths=0.3, 
                     patch_artist=True, tick_labels=['' for _ in metric_names])
    bp2 = ax.boxplot(simplified_metrics, positions=positions_simplified, widths=0.3, 
                     patch_artist=True, tick_labels=['' for _ in metric_names])
    
    # Set colors
    for patch in bp1['boxes']:
        patch.set_facecolor(complex_color)
        patch.set_alpha(0.7)
    
    for patch in bp2['boxes']:
        patch.set_facecolor(simplified_color)
        patch.set_alpha(0.7)
    
    ax.set_title('Comprehensive Performance Distribution', fontsize=14, fontweight='bold')
    ax.set_xlabel('Performance Metrics')
    ax.set_ylabel('Normalized Score')
    ax.set_xticks(range(len(metric_names)))
    ax.set_xticklabels(metric_names, fontsize=10)
    ax.set_ylim(-0.1, 1.1)
    ax.grid(True, alpha=0.3)
    
    # Add legend
    from matplotlib.patches import Patch
    legend_elements = [Patch(facecolor=complex_color, alpha=0.7, label='Complex Reward'),
                      Patch(facecolor=simplified_color, alpha=0.7, label='Simplified Reward')]
    ax.legend(handles=legend_elements, fontsize=12)
    
    plt.tight_layout()
    
    # Save chart
    save_path = os.path.join(save_dir, f'{scenario_name}_detailed_analysis_en.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"✅ Scenario {scenario_name} analysis chart saved: {save_path}")
    
    # Print numerical comparison summary
    print(f"\n📈 Scenario {scenario_name} Numerical Comparison Summary:")
    print("-" * 50)
    print(f"Success Rate: Complex={final_complex:.1%}, Simplified={final_simplified:.1%}")
    print(f"Avg Path Length: Complex={np.mean(complex_data['path_length']):.1f}m, Simplified={np.mean(simplified_data['path_length']):.1f}m")
    print(f"Avg Completion Time: Complex={np.mean(complex_data['completion_time']):.1f}s, Simplified={np.mean(simplified_data['completion_time']):.1f}s")
    print(f"Avg Energy Consumption: Complex={np.mean(complex_data['energy_consumption']):.1f}, Simplified={np.mean(simplified_data['energy_consumption']):.1f}")
    print(f"Avg Path Efficiency: Complex={np.mean(complex_data['efficiency']):.3f}, Simplified={np.mean(simplified_data['efficiency']):.3f}")

def generate_all_scenario_analyses(results_path, output_dir='scenario_analyses'):
    """Generate detailed analysis for all scenarios"""
    print("🎯 Starting detailed performance comparison analysis by scenario")
    print("=" * 60)
    
    # Create output directory
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Load data
    data = load_test_results(results_path)
    episode_data = data['episode_data']
    
    # Organize data by scenario
    scenario_data = organize_data_by_scenario(episode_data)
    
    print(f"📊 Found {len(scenario_data)} test scenarios")
    
    # Generate analysis for each scenario
    for scenario_name, data in scenario_data.items():
        generate_scenario_analysis(scenario_name, data, output_dir)
    
    print(f"\n🎉 All scenario analyses completed!")
    print(f"📁 Analysis charts saved to: {output_dir}/")
    print(f"📋 Generated files:")
    for scenario_name in scenario_data.keys():
        print(f"   • {scenario_name}_detailed_analysis_en.png")

def main():
    """Main function"""
    # Find latest test results
    import glob
    result_files = glob.glob('enhanced_performance_test_*/enhanced_performance_results.json')
    
    if not result_files:
        print("❌ Cannot find enhanced test result files")
        print("Please run enhanced_performance_test.py first")
        return
    
    # Use latest result file
    latest_result = max(result_files, key=os.path.getctime)
    print(f"🔍 Using latest test results: {latest_result}")
    
    # Generate detailed analysis for all scenarios
    generate_all_scenario_analyses(latest_result)

if __name__ == "__main__":
    main() 