# 🚀 新训练对比系统使用指南

## 📋 系统概述

本系统包含两个主要工具：
1. **dedicated_training_comparison.py** - 专用训练对比器（分别训练2000次）
2. **performance_comparison_test.py** - 性能对比测试器（实际性能评估）

## 🎯 快速开始

### 第一步：运行专用训练对比
```bash
# 运行完整的训练对比（每种奖励函数训练2000次）
python dedicated_training_comparison.py

# 或者自定义episodes数量
python dedicated_training_comparison.py --episodes 500

# 或者使用较少的episodes进行快速测试
python dedicated_training_comparison.py --episodes 50
```

**预期输出**：
- 训练过程实时显示
- 两种奖励函数的详细对比分析
- 模型文件保存到 `dedicated_comparison_YYYYMMDD_HHMMSS/` 目录

**⏱️ 预计训练时间**：
- 2000次训练大约需要 **2-4小时**（取决于硬件配置）
- 建议在有充足时间时运行，或者先用较少episodes测试

### 第二步：运行性能对比测试
```bash
# 自动查找最新的训练结果进行测试
python performance_comparison_test.py --test-episodes 50

# 或者指定特定的训练结果目录
python performance_comparison_test.py --model-dir dedicated_comparison_20250702_123456 --test-episodes 50
```

**预期输出**：
- 多个测试场景的性能对比
- 路径效率、时间、能量等详细指标
- 测试结果保存到 `performance_test_YYYYMMDD_HHMMSS/` 目录

## 📊 输出文件说明

### 训练对比输出 (dedicated_comparison_*)
```
dedicated_comparison_20250702_123456/
├── complex_model.pth              # 复杂奖励函数训练的模型
├── simplified_model.pth           # 简化奖励函数训练的模型
├── complex_training_data.pkl      # 复杂奖励函数的训练数据
├── simplified_training_data.pkl   # 简化奖励函数的训练数据
├── complex_training_report.json   # 复杂奖励函数的训练报告
├── simplified_training_report.json # 简化奖励函数的训练报告
└── deep_comparison_analysis.json  # 深度对比分析报告
```

### 性能测试输出 (performance_test_*)
```
performance_test_20250702_134567/
└── performance_test_results.json  # 详细的性能测试结果
```

## 🔍 结果分析

### 训练阶段对比指标
- **成功率**：模型成功完成任务的比例
- **奖励稳定性**：变异系数(CV)，越小越稳定
- **收敛速度**：模型收敛所需的episode数
- **学习改善**：前20次vs后20次的性能提升
- **最终性能**：最后50次的成功率

### 性能测试对比指标
- **路径效率**：直线距离/实际路径长度
- **完成时间**：任务完成的平均时间
- **能量消耗**：基于步数的简化能量模型
- **安全边距**：与障碍物的最小距离
- **成功率**：在不同场景下的成功率

## 🧪 测试场景

系统包含多个测试场景：

1. **Standard**: 标准场景，中等难度
2. **Dense_Obstacles**: 密集障碍物场景
3. **Narrow_Passage**: 狭窄通道场景

每个场景都会测试两种模型的表现。

## 📈 预期结果

根据我们之前的快速测试，预期简化奖励函数会显示：
- ✅ **更高的成功率**
- ✅ **更好的训练稳定性**
- ✅ **更快的收敛速度**
- ✅ **更高的路径效率**
- ✅ **更短的完成时间**

## 🛠️ 高级用法

### 自定义训练参数
```bash
# 使用不同的随机种子
python dedicated_training_comparison.py --episodes 2000 --seed 123

# 训练更多episodes（超长训练）
python dedicated_training_comparison.py --episodes 5000

# 快速测试版本
python dedicated_training_comparison.py --episodes 100
```

### 自定义测试参数
```bash
# 增加测试episodes获得更稳定的结果
python performance_comparison_test.py --test-episodes 100

# 测试特定的训练结果
python performance_comparison_test.py --model-dir your_training_dir --test-episodes 50
```

## 🔧 故障排除

### 问题1：找不到模型文件
**现象**：`FileNotFoundError: 在目录中找不到模型文件`
**解决**：
1. 确保已运行训练对比脚本
2. 检查目录名是否正确
3. 使用 `--model-dir` 参数指定正确的目录

### 问题2：训练过程中断
**现象**：训练突然停止
**解决**：
1. 检查内存使用情况
2. 减少episodes数量进行测试
3. 关闭不必要的可视化

### 问题3：结果不符合预期
**现象**：简化奖励函数表现不如复杂版本
**解决**：
1. 增加训练episodes数量
2. 检查随机种子设置
3. 运行多次实验取平均值

## 📞 获取帮助

如果遇到问题，请提供：
1. 完整的错误信息
2. 运行的命令
3. 生成的日志文件
4. 系统环境信息

## 🎉 完整的实验流程示例

```bash
# 1. 运行训练对比（每种奖励函数2000次，总共4000次训练）
python dedicated_training_comparison.py

# 等待训练完成（预计2-4小时），查看输出的对比分析

# 2. 运行性能测试
python performance_comparison_test.py --test-episodes 50

# 3. 查看结果
# 检查生成的JSON文件和控制台输出
```

**⚠️ 重要提醒**：
- 2000次训练需要较长时间，建议在夜间或有充足时间时运行
- 可以先用 `--episodes 50` 进行快速测试，确认系统工作正常
- 训练过程中可以观察控制台输出来监控进度

这个完整的流程将为您提供两种奖励函数设计的全面对比分析，包括训练过程和实际性能的各个方面。 