"""
DWA-RL核心模块
包含环境、控制器等公共组件，供训练和测试脚本使用
"""

import numpy as np
import torch
import torch.nn.functional as F
from collections import deque
import random
import json
import os
from datetime import datetime

from td3_dwa_rl_architecture import TD3_DWA_RL_Controller, td3_config
from simple_environment import SimpleUAVEnvironment

class ImprovedReplayBuffer:
    """改进的经验回放缓冲区 - 缓存DWA动作集"""
    def __init__(self, capacity=100000):
        self.capacity = capacity
        self.buffer = deque(maxlen=capacity)

    def add(self, state, action, reward, next_state, done, safe_actions, goal, obstacles, action_idx):
        """添加经验 - 包含完整的安全动作集和选择索引"""
        experience = {
            'state': state,
            'action': action,
            'reward': reward,
            'next_state': next_state,
            'done': done,
            'safe_actions': safe_actions,
            'goal': goal,
            'obstacles': obstacles,
            'action_idx': action_idx
        }
        self.buffer.append(experience)

    def sample(self, batch_size):
        """随机采样经验"""
        return random.sample(self.buffer, min(batch_size, len(self.buffer)))

    def __len__(self):
        return len(self.buffer)

class StabilizedEnvironment(SimpleUAVEnvironment):
    """稳定化的环境 - 修复奖励波动问题，支持复杂障碍物"""

    def __init__(self, enable_dynamic_obstacles=False, environment_config=None, reward_type='simplified'):
        # 添加奖励稳定化参数
        self.reward_scale = 1.0  # 奖励缩放因子
        self.max_distance_improvement = 5.0  # 限制单步最大距离改善奖励
        self.environment_config = environment_config  # 环境配置
        self.reward_type = reward_type  # 奖励函数类型：'simplified' 或 'complex'

        # 约束量记录
        self.constraint_history = {
            'velocities': [],
            'accelerations': [],
            'positions': [],
            'distances_to_obstacles': [],
            'goal_distances': [],
            'constraint_violations': []
        }

        # 约束限制 - 与DWA配置保持数学一致性
        # DWA使用分量约束[3,3,3]，对应的合速度限制应为√(3²+3²+3²) = √27 ≈ 5.196
        self.max_velocity_components = [3.0, 3.0, 3.0]  # 速度分量约束 (与DWA一致)
        self.max_velocity = np.sqrt(sum([v**2 for v in self.max_velocity_components]))  # 合速度约束

        # DWA使用分量约束[5,5,5]，对应的合加速度限制应为√(5²+5²+5²) = √75 ≈ 8.660
        self.max_acceleration_components = [5.0, 5.0, 5.0]  # 加速度分量约束 (与DWA一致)
        self.max_acceleration = np.sqrt(sum([a**2 for a in self.max_acceleration_components]))  # 合加速度约束

        self.min_obstacle_distance = 3.0  # 最小障碍物距离约束

        super().__init__(enable_dynamic_obstacles=enable_dynamic_obstacles, environment_config=environment_config)

    def _calculate_reward(self):
        """
        可配置的奖励函数 - 支持复杂和简化两种模式
        使用实例的reward_type属性
        """
        if self.reward_type == 'simplified':
            return self._calculate_reward_simplified()
        else:
            return self._calculate_reward_complex()

    def _calculate_reward_simplified(self):
        """简化的奖励函数 - 参考701文件夹设计，目标明确"""
        pos = self.state[:3]

        # 1. 强烈的终止奖励/惩罚（明确的成功/失败信号）
        goal_dist = np.linalg.norm(pos - self.goal)

        # 到达目标：大奖励
        if goal_dist < 5.0:
            return 100.0, True, {'success': True, 'reason': 'goal_reached'}

        # 碰撞：大惩罚
        for obs in self.obstacles:
            dist = np.linalg.norm(pos - obs['center'])
            if dist <= obs['radius']:
                return -100.0, True, {'collision': True, 'reason': 'static_collision'}

        for obs in self.dynamic_obstacles:
            dist = np.linalg.norm(pos - obs['center'])
            if dist <= obs['radius']:
                return -100.0, True, {'collision': True, 'reason': 'dynamic_collision'}

        # 越界：大惩罚
        if (pos < 0).any() or (pos > self.bounds).any():
            return -100.0, True, {'out_of_bounds': True, 'reason': 'out_of_bounds'}

        # 2. 核心奖励：距离目标的负值（强烈鼓励接近目标）
        distance_reward = -goal_dist / 50.0  # 主导信号，范围约[-2.0, 0.0]

        # 3. 效率奖励（鼓励快速完成）
        efficiency_reward = -0.1  # 每步小惩罚，鼓励快速到达

        # 4. 最小安全约束（仅在危险时惩罚）
        min_obs_dist = float('inf')
        for obs in self.obstacles:
            dist = np.linalg.norm(pos - obs['center']) - obs['radius']
            min_obs_dist = min(min_obs_dist, dist)
        for obs in self.dynamic_obstacles:
            dist = np.linalg.norm(pos - obs['center']) - obs['radius']
            min_obs_dist = min(min_obs_dist, dist)

        danger_penalty = 0
        if min_obs_dist < 3.0:  # 只在真正接近危险时惩罚
            danger_penalty = -(3.0 - min_obs_dist) * 2.0  # 强烈惩罚

        # 超时检查
        done = self.step_count >= self.max_steps
        if done:
            info = {'timeout': True, 'reason': 'timeout'}
        else:
            info = {}

        # 简洁的总奖励：主要由距离主导
        total_reward = distance_reward + efficiency_reward + danger_penalty

        return total_reward, done, info

    def _calculate_reward_complex(self):
        """复杂奖励函数 - 原始多目标设计"""
        pos = self.state[:3]
        vel = self.state[3:6]

        # 计算当前到目标的距离
        goal_dist = np.linalg.norm(pos - self.goal)

        # 计算距离改善（如果有前一步记录）
        prev_goal_dist = getattr(self, '_prev_goal_dist', goal_dist)
        distance_improvement = prev_goal_dist - goal_dist
        self._prev_goal_dist = goal_dist

        # 1. 强烈的终止奖励
        if goal_dist < 5.0:
            return 100.0, True, {'success': True, 'reason': 'goal_reached'}

        # 2. 碰撞惩罚
        for obs in self.obstacles:
            dist = np.linalg.norm(pos - obs['center'])
            if dist <= obs['radius']:
                return -100.0, True, {'collision': True, 'reason': 'static_collision'}

        for obs in self.dynamic_obstacles:
            dist = np.linalg.norm(pos - obs['center'])
            if dist <= obs['radius']:
                return -100.0, True, {'collision': True, 'reason': 'dynamic_collision'}

        # 3. 边界惩罚
        if (pos < 0).any() or (pos > self.bounds).any():
            return -50.0, True, {'out_of_bounds': True, 'reason': 'out_of_bounds'}

        # 4. 核心奖励设计 - 让信号更强
        # 距离奖励：使用更强的距离改善信号
        distance_reward = distance_improvement * 10.0  # 放大10倍

        # 目标导向奖励：使用反比例函数，越近奖励越高
        goal_orientation_reward = 20.0 / (1.0 + goal_dist * 0.1)  # 范围[0, 20]

        # 速度奖励：鼓励朝向目标的运动
        speed = np.linalg.norm(vel)
        if speed > 0.1 and goal_dist > 0:
            goal_direction = (self.goal - pos) / goal_dist
            vel_direction = vel / speed
            direction_bonus = max(0, np.dot(goal_direction, vel_direction)) * 2.0
        else:
            direction_bonus = 0

        # 效率奖励：距离目标越近，基础奖励越高
        progress_bonus = max(0, (121.2 - goal_dist) / 121.2 * 5.0)  # 范围[0, 5]

        # 轻微的时间惩罚（大幅减少）
        time_penalty = -0.001  # 从-0.01减少到-0.001

        # 安全奖励（简化）
        min_obs_dist = float('inf')
        for obs in self.obstacles:
            dist = np.linalg.norm(pos - obs['center']) - obs['radius']
            min_obs_dist = min(min_obs_dist, dist)
        for obs in self.dynamic_obstacles:
            dist = np.linalg.norm(pos - obs['center']) - obs['radius']
            min_obs_dist = min(min_obs_dist, dist)

        # 只有在危险时才给负奖励
        if min_obs_dist < 5.0:
            safety_penalty = -(5.0 - min_obs_dist) * 0.5
        else:
            safety_penalty = 0

        # 超时检查
        done = self.step_count >= self.max_steps
        if done:
            info = {'timeout': True, 'reason': 'timeout'}
        else:
            info = {}

        # 总奖励计算：让正向信号更强
        total_reward = (
            distance_reward +           # 距离改善 (主导信号)
            goal_orientation_reward +   # 目标导向奖励
            direction_bonus +           # 方向奖励
            progress_bonus +            # 进度奖励
            time_penalty +              # 时间惩罚(很小)
            safety_penalty              # 安全惩罚(仅危险时)
        )

        return total_reward, done, info

    def step(self, action):
        """重写step方法以记录约束量"""
        # 记录当前状态
        prev_velocity = self.state[3:6].copy()
        prev_position = self.state[:3].copy()

        # 执行原始step
        observation, reward, done, info = super().step(action)

        # 计算约束量
        current_velocity = self.state[3:6]
        current_position = self.state[:3]

        # 计算加速度 (当前速度 - 前一步速度) / dt
        dt = 0.1
        acceleration = (current_velocity - prev_velocity) / dt

        # 计算速度大小
        velocity_magnitude = np.linalg.norm(current_velocity)
        acceleration_magnitude = np.linalg.norm(acceleration)

        # 计算到障碍物的最小距离
        min_obstacle_dist = float('inf')
        for obs in self.obstacles:
            dist = np.linalg.norm(current_position - obs['center']) - obs['radius']
            min_obstacle_dist = min(min_obstacle_dist, dist)

        # 计算到目标的距离
        goal_distance = np.linalg.norm(current_position - self.goal)

        # 检查约束违反 - 同时检查分量和合量约束
        velocity_component_violations = [
            abs(current_velocity[i]) > self.max_velocity_components[i] for i in range(3)
        ]
        acceleration_component_violations = [
            abs(acceleration[i]) > self.max_acceleration_components[i] for i in range(3)
        ]

        violations = {
            'velocity_magnitude_violation': velocity_magnitude > self.max_velocity,
            'velocity_component_violations': velocity_component_violations,
            'velocity_any_component_violation': any(velocity_component_violations),
            'acceleration_magnitude_violation': acceleration_magnitude > self.max_acceleration,
            'acceleration_component_violations': acceleration_component_violations,
            'acceleration_any_component_violation': any(acceleration_component_violations),
            'obstacle_violation': min_obstacle_dist < self.min_obstacle_distance,
            'boundary_violation': (current_position < 0).any() or (current_position > self.bounds).any()
        }

        # 记录约束量
        self.constraint_history['velocities'].append(velocity_magnitude)
        self.constraint_history['accelerations'].append(acceleration_magnitude)
        self.constraint_history['positions'].append(current_position.copy())
        self.constraint_history['distances_to_obstacles'].append(max(0, min_obstacle_dist))
        self.constraint_history['goal_distances'].append(goal_distance)
        self.constraint_history['constraint_violations'].append(violations)

        return observation, reward, done, info

    def reset(self):
        """重写reset方法以清空约束历史"""
        observation = super().reset()
        # 清空约束历史
        for key in self.constraint_history:
            self.constraint_history[key] = []
        return observation

    def get_constraint_summary(self):
        """获取约束量统计摘要"""
        if not self.constraint_history['velocities']:
            return {}

        velocities = np.array(self.constraint_history['velocities'])
        accelerations = np.array(self.constraint_history['accelerations'])
        obstacle_distances = np.array(self.constraint_history['distances_to_obstacles'])

        # 统计违反次数 - 更新为新的约束检查格式
        violations = self.constraint_history['constraint_violations']
        velocity_magnitude_violations = sum(1 for v in violations if v.get('velocity_magnitude_violation', False))
        velocity_component_violations = sum(1 for v in violations if v.get('velocity_any_component_violation', False))
        acceleration_magnitude_violations = sum(1 for v in violations if v.get('acceleration_magnitude_violation', False))
        acceleration_component_violations = sum(1 for v in violations if v.get('acceleration_any_component_violation', False))
        obstacle_violations = sum(1 for v in violations if v.get('obstacle_violation', False))
        boundary_violations = sum(1 for v in violations if v.get('boundary_violation', False))

        return {
            'velocity_stats': {
                'max': np.max(velocities),
                'mean': np.mean(velocities),
                'std': np.std(velocities),
                'magnitude_violations': velocity_magnitude_violations,
                'magnitude_violation_rate': velocity_magnitude_violations / len(velocities),
                'component_violations': velocity_component_violations,
                'component_violation_rate': velocity_component_violations / len(velocities),
                'max_velocity_limit': self.max_velocity,
                'velocity_component_limits': self.max_velocity_components
            },
            'acceleration_stats': {
                'max': np.max(accelerations),
                'mean': np.mean(accelerations),
                'std': np.std(accelerations),
                'magnitude_violations': acceleration_magnitude_violations,
                'magnitude_violation_rate': acceleration_magnitude_violations / len(accelerations),
                'component_violations': acceleration_component_violations,
                'component_violation_rate': acceleration_component_violations / len(accelerations),
                'max_acceleration_limit': self.max_acceleration,
                'acceleration_component_limits': self.max_acceleration_components
            },
            'obstacle_distance_stats': {
                'min': np.min(obstacle_distances),
                'mean': np.mean(obstacle_distances),
                'std': np.std(obstacle_distances),
                'violations': obstacle_violations,
                'violation_rate': obstacle_violations / len(obstacle_distances)
            },
            'total_steps': len(velocities),
            'total_violations': velocity_magnitude_violations + acceleration_magnitude_violations + obstacle_violations + boundary_violations
        }

class StabilizedTD3Controller(TD3_DWA_RL_Controller):
    """稳定化的TD3控制器"""

    def __init__(self, config):
        # 修改配置以增加稳定性
        stable_config = config.copy()
        stable_config['actor_lr'] = 0.0001  # 降低学习率
        stable_config['critic_lr'] = 0.0005  # 降低学习率
        stable_config['policy_noise'] = 0.1  # 减少策略噪声
        stable_config['noise_clip'] = 0.3    # 减少噪声裁剪
        stable_config['tau'] = 0.001         # 更保守的软更新

        super().__init__(stable_config)

        # 替换为改进的经验池
        self.replay_buffer = ImprovedReplayBuffer(capacity=config['buffer_size'])

        # 增加更新频率控制
        self.update_frequency = 8  # 从4增加到8，减少更新频率
        self.min_buffer_size = 1000
        self.step_counter = 0

        # 动作质量评估
        self.action_quality_history = deque(maxlen=1000)

        # 训练监控指标
        self.step_rewards = deque(maxlen=10000)
        self.batch_rewards = deque(maxlen=1000)
        self.q_values = deque(maxlen=1000)
        self.critic_losses = deque(maxlen=1000)
        self.actor_losses = deque(maxlen=1000)

    def get_action_with_quality(self, state, goal, obstacles, add_noise=True):
        """获取动作并评估选择质量（默认使用噪声以避免超时问题）"""
        # DWA生成安全动作集
        safe_actions = self.safe_action_generator.generate_safe_action_set(
            state, goal, obstacles, target_actions=self.config['max_actions']
        )

        if len(safe_actions) == 0:
            return [0, 0, 0], {'num_safe_actions': 0, 'quality_score': 0}, []

        # 准备输入
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        actions_tensor = torch.FloatTensor(np.array([action['velocity'] for action in safe_actions])).unsqueeze(0)

        # 填充到固定长度
        num_actions = len(safe_actions)
        if num_actions < self.config['max_actions']:
            padding = np.zeros((self.config['max_actions'] - num_actions, 3))
            actions_tensor = torch.cat([
                actions_tensor,
                torch.FloatTensor(padding).unsqueeze(0)
            ], dim=1)

        actions_tensor = actions_tensor.to(self.device)

        # 创建mask
        mask = torch.ones(1, self.config['max_actions'], dtype=torch.bool).to(self.device)
        if num_actions < self.config['max_actions']:
            mask[0, num_actions:] = False

        # Actor选择动作
        with torch.no_grad():
            probabilities = self.actor.get_action_probabilities(
                state_tensor, actions_tensor, mask, temperature=0.1 if not add_noise else 1.0
            )

            if add_noise:
                noise = torch.randn_like(probabilities) * 0.1
                probabilities = F.softmax(probabilities + noise, dim=-1)

            action_idx = probabilities.argmax().item()

        selected_action = safe_actions[action_idx] if action_idx < len(safe_actions) else safe_actions[0]

        # 计算动作质量分数（相对于DWA最优动作的比较）
        best_dwa_score = max([action['total_score'] for action in safe_actions])
        selected_score = selected_action['total_score']
        quality_score = selected_score / best_dwa_score if best_dwa_score > 0 else 1.0

        # 减少质量奖励的影响
        quality_score = min(quality_score, 1.0)  # 确保不超过1.0

        self.action_quality_history.append(quality_score)

        return selected_action['velocity'], {
            'safe_actions': safe_actions,
            'selected_idx': action_idx,
            'num_safe_actions': len(safe_actions),
            'action_probabilities': probabilities.cpu().numpy(),
            'quality_score': quality_score
        }, safe_actions

    def immediate_update(self, batch_size=32):
        """即时训练更新"""
        if len(self.replay_buffer) < self.min_buffer_size:
            return None, None

        self.step_counter += 1

        # 每update_frequency步更新一次
        if self.step_counter % self.update_frequency != 0:
            return None, None

        # 小批量更新
        return self.train_step_improved(batch_size)

    def train_step_improved(self, batch_size=64):
        """改进的训练步骤 - 使用缓存的DWA动作集"""
        if len(self.replay_buffer) < batch_size:
            return None, None

        # 采样经验
        batch = self.replay_buffer.sample(batch_size)

        # 准备批次数据
        states = torch.FloatTensor(np.array([exp['state'] for exp in batch])).to(self.device)
        actions = torch.FloatTensor(np.array([exp['action'] for exp in batch])).to(self.device)
        rewards = torch.FloatTensor(np.array([exp['reward'] for exp in batch])).to(self.device)
        next_states = torch.FloatTensor(np.array([exp['next_state'] for exp in batch])).to(self.device)
        dones = torch.BoolTensor(np.array([exp['done'] for exp in batch])).to(self.device)

        with torch.no_grad():
            # 使用缓存的安全动作集进行下一状态动作选择
            next_actions = []
            for exp in batch:
                if exp['safe_actions'] and len(exp['safe_actions']) > 0:
                    # 从缓存的安全动作集中随机选择（模拟策略噪声）
                    safe_velocities = [action['velocity'] for action in exp['safe_actions']]
                    if len(safe_velocities) > 1:
                        # 添加策略噪声：在安全动作集中随机选择
                        noise_idx = np.random.randint(0, len(safe_velocities))
                        next_action = safe_velocities[noise_idx]
                    else:
                        next_action = safe_velocities[0]

                    # 添加小量噪声
                    next_action = np.array(next_action) + np.random.normal(0, 0.05, 3)
                    next_action = np.clip(next_action, -5, 5)
                else:
                    # 回退方案：使用当前动作加噪声
                    next_action = np.array(exp['action']) + np.random.normal(0, 0.1, 3)
                    next_action = np.clip(next_action, -5, 5)

                next_actions.append(next_action)

            next_actions = torch.FloatTensor(np.array(next_actions)).to(self.device)

            # 目标策略平滑
            noise = (torch.randn_like(next_actions) * self.policy_noise).clamp(
                -self.noise_clip, self.noise_clip
            )
            next_actions = (next_actions + noise).clamp(-5, 5)

            # 计算目标Q值（取两个Q网络的最小值）
            target_q1, target_q2 = self.target_critic(next_states, next_actions)
            target_q = torch.min(target_q1, target_q2)
            target_q = rewards.unsqueeze(1) + (self.gamma * target_q * (~dones).unsqueeze(1))

        # 当前Q值
        current_q1, current_q2 = self.critic(states, actions)

        # Critic损失
        critic_loss = F.mse_loss(current_q1, target_q) + F.mse_loss(current_q2, target_q)

        # 更新Critic
        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        self.critic_optimizer.step()

        # 记录指标
        self.critic_losses.append(critic_loss.item())
        self.q_values.append(current_q1.mean().item())

        actor_loss = None
        # 延迟策略更新
        if self.step_counter % self.policy_freq == 0:
            # 使用缓存的安全动作集进行Actor更新
            actor_actions = []
            for exp in batch:
                if exp['safe_actions'] and len(exp['safe_actions']) > 0:
                    # 准备安全动作集
                    safe_velocities = [action['velocity'] for action in exp['safe_actions']]
                    state_tensor = torch.FloatTensor(exp['state']).unsqueeze(0).to(self.device)
                    actions_tensor = torch.FloatTensor(np.array(safe_velocities)).unsqueeze(0)

                    # 填充
                    num_actions = len(safe_velocities)
                    if num_actions < self.config['max_actions']:
                        padding = np.zeros((self.config['max_actions'] - num_actions, 3))
                        actions_tensor = torch.cat([
                            actions_tensor,
                            torch.FloatTensor(padding).unsqueeze(0)
                        ], dim=1)

                    actions_tensor = actions_tensor.to(self.device)

                    # 创建mask
                    mask = torch.ones(1, self.config['max_actions'], dtype=torch.bool).to(self.device)
                    if num_actions < self.config['max_actions']:
                        mask[0, num_actions:] = False

                    # 获取Actor选择的动作
                    with torch.no_grad():
                        logits = self.actor(state_tensor, actions_tensor, mask)
                        action_idx = logits.argmax().item()
                        if action_idx < len(safe_velocities):
                            actor_action = safe_velocities[action_idx]
                        else:
                            actor_action = safe_velocities[0]
                else:
                    # 回退方案
                    actor_action = exp['action']

                actor_actions.append(actor_action)

            actor_actions = torch.FloatTensor(np.array(actor_actions)).to(self.device)

            # Actor损失（策略梯度）
            q1_values, _ = self.critic(states, actor_actions)
            actor_loss = -q1_values.mean()

            # 更新Actor
            self.actor_optimizer.zero_grad()
            actor_loss.backward()
            self.actor_optimizer.step()

            # 软更新目标网络
            self.soft_update(self.critic, self.target_critic, self.tau)
            self.soft_update(self.actor, self.target_actor, self.tau)

            self.actor_losses.append(actor_loss.item())

        return critic_loss.item(), actor_loss.item() if actor_loss is not None else None

    def soft_update(self, local_model, target_model, tau):
        """软更新目标网络"""
        for target_param, local_param in zip(target_model.parameters(), local_model.parameters()):
            target_param.data.copy_(tau * local_param.data + (1.0 - tau) * target_param.data)

    def save_model(self, path):
        """保存模型"""
        torch.save({
            'actor_state_dict': self.actor.state_dict(),
            'critic_state_dict': self.critic.state_dict(),
            'actor_optimizer_state_dict': self.actor_optimizer.state_dict(),
            'critic_optimizer_state_dict': self.critic_optimizer.state_dict(),
            'config': self.config
        }, path)

def load_trained_model(model_path, config):
    """加载训练好的模型"""
    # 创建控制器
    controller = StabilizedTD3Controller(config)

    # 加载模型权重
    checkpoint = torch.load(model_path, map_location=controller.device, weights_only=False)
    controller.actor.load_state_dict(checkpoint['actor_state_dict'])
    controller.critic.load_state_dict(checkpoint['critic_state_dict'])

    # 设置为评估模式
    controller.actor.eval()
    controller.critic.eval()

    print(f"✅ 模型已加载: {model_path}")
    return controller