"""
简化奖励函数分阶段训练脚本
将701文件夹中的简单奖励函数应用于分阶段训练场景
实现从简单到复杂的渐进式训练，使用简化的三项奖励函数
"""

import os
import argparse
from datetime import datetime
import json
import numpy as np

from train_dwa_rl import train_dwa_rl_model
from environment_config import TRAINING_STAGES, get_training_stage_config, get_environment_config

def simplified_reward_staged_training(start_stage=1, end_stage=3, save_outputs=True, compare_rewards=False):
    """
    使用简化奖励函数的分阶段训练
    
    Args:
        start_stage: 开始阶段 (1-3)
        end_stage: 结束阶段 (1-3)
        save_outputs: 是否保存输出
        compare_rewards: 是否同时对比复杂奖励函数
    """
    print("🎯 简化奖励函数分阶段训练")
    print("=" * 60)
    print("📋 训练特点:")
    print("• 使用701文件夹中设计的简化奖励函数")
    print("• 三项奖励：距离目标 + 效率激励 + 安全约束")
    print("• 强烈的终止信号：±100奖励")
    print("• 目标明确：主要优化全局路径")
    print("=" * 60)
    
    # 阶段配置映射
    stage_configs = {
        1: "stage1_basic",
        2: "stage2_complex_static", 
        3: "stage3_dynamic_adaptation"
    }
    
    # 验证阶段参数
    if start_stage < 1 or start_stage > 3 or end_stage < 1 or end_stage > 3:
        raise ValueError("阶段必须在1-3之间")
    if start_stage > end_stage:
        raise ValueError("开始阶段不能大于结束阶段")
    
    # 创建输出目录
    if save_outputs:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"simplified_reward_training_{timestamp}"
        os.makedirs(output_dir, exist_ok=True)
        print(f"📁 输出目录: {output_dir}")
    
    # 训练结果记录
    training_results = {
        "experiment_type": "simplified_reward_staged_training",
        "start_time": datetime.now().isoformat(),
        "reward_function_type": "simplified",
        "stages": {},
        "overall_performance": {}
    }
    
    # 逐阶段训练（简化奖励函数）
    print(f"\n🚀 开始简化奖励函数训练")
    print("=" * 50)
    
    for stage_num in range(start_stage, end_stage + 1):
        stage_key = stage_configs[stage_num]
        stage_config = get_training_stage_config(stage_key)
        env_config = get_environment_config(stage_config["environment"])
        
        print(f"\n📈 阶段 {stage_num}: {stage_config['description']}")
        print("=" * 40)
        print(f"环境: {stage_config['environment']}")
        print(f"Episodes: {stage_config['episodes']}")
        print(f"奖励函数: 简化版本（三项奖励）")
        print(f"环境描述: {env_config['description']}")
        
        # 训练当前阶段
        try:
            episode_rewards, step_rewards, controller, constraint_data = train_dwa_rl_model(
                num_episodes=stage_config['episodes'],
                enable_visualization=False,  # 分阶段训练时禁用可视化以提高速度
                save_outputs=save_outputs,
                environment_config=stage_config['environment'],
                reward_type='simplified'  # 使用简化奖励函数
            )
            
            # 记录阶段结果
            stage_results = {
                "environment": stage_config['environment'],
                "episodes": stage_config['episodes'],
                "reward_type": "simplified",
                "final_avg_reward": float(sum(episode_rewards[-10:]) / min(10, len(episode_rewards))),
                "total_episodes": len(episode_rewards),
                "success_rate": len([r for r in episode_rewards if r > 0]) / len(episode_rewards),
                "reward_std": float(np.std(episode_rewards)),
                "reward_cv": float(np.std(episode_rewards) / abs(np.mean(episode_rewards))) if np.mean(episode_rewards) != 0 else 0,
                "max_reward": float(np.max(episode_rewards)),
                "min_reward": float(np.min(episode_rewards)),
                "constraint_violations": len([v for v in constraint_data.get('constraint_violations', []) if any(v.values())])
            }
            
            training_results["stages"][f"stage_{stage_num}_simplified"] = stage_results
            
            print(f"✅ 阶段 {stage_num} 完成")
            print(f"   平均奖励: {stage_results['final_avg_reward']:.2f}")
            print(f"   成功率: {stage_results['success_rate']:.2%}")
            print(f"   奖励稳定性(CV): {stage_results['reward_cv']:.3f}")
            
        except Exception as e:
            print(f"❌ 阶段 {stage_num} 训练失败: {e}")
            training_results["stages"][f"stage_{stage_num}_simplified"] = {"error": str(e)}
            break
    
    # 对比训练（如果启用）
    if compare_rewards:
        print(f"\n🔄 开始复杂奖励函数对比训练")
        print("=" * 50)
        
        for stage_num in range(start_stage, end_stage + 1):
            stage_key = stage_configs[stage_num]
            stage_config = get_training_stage_config(stage_key)
            env_config = get_environment_config(stage_config["environment"])
            
            print(f"\n📊 对比阶段 {stage_num}: {stage_config['description']}")
            print("=" * 40)
            print(f"奖励函数: 复杂版本（多目标优化）")
            
            try:
                episode_rewards, step_rewards, controller, constraint_data = train_dwa_rl_model(
                    num_episodes=stage_config['episodes'],
                    enable_visualization=False,
                    save_outputs=save_outputs,
                    environment_config=stage_config['environment'],
                    reward_type='complex'  # 使用复杂奖励函数
                )
                
                # 记录对比结果
                stage_results = {
                    "environment": stage_config['environment'],
                    "episodes": stage_config['episodes'],
                    "reward_type": "complex",
                    "final_avg_reward": float(sum(episode_rewards[-10:]) / min(10, len(episode_rewards))),
                    "total_episodes": len(episode_rewards),
                    "success_rate": len([r for r in episode_rewards if r > 0]) / len(episode_rewards),
                    "reward_std": float(np.std(episode_rewards)),
                    "reward_cv": float(np.std(episode_rewards) / abs(np.mean(episode_rewards))) if np.mean(episode_rewards) != 0 else 0,
                    "max_reward": float(np.max(episode_rewards)),
                    "min_reward": float(np.min(episode_rewards)),
                    "constraint_violations": len([v for v in constraint_data.get('constraint_violations', []) if any(v.values())])
                }
                
                training_results["stages"][f"stage_{stage_num}_complex"] = stage_results
                
                print(f"✅ 对比阶段 {stage_num} 完成")
                print(f"   平均奖励: {stage_results['final_avg_reward']:.2f}")
                print(f"   成功率: {stage_results['success_rate']:.2%}")
                print(f"   奖励稳定性(CV): {stage_results['reward_cv']:.3f}")
                
            except Exception as e:
                print(f"❌ 对比阶段 {stage_num} 训练失败: {e}")
                training_results["stages"][f"stage_{stage_num}_complex"] = {"error": str(e)}
    
    # 保存训练结果
    if save_outputs:
        training_results["end_time"] = datetime.now().isoformat()
        results_path = os.path.join(output_dir, "simplified_reward_training_results.json")
        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(training_results, f, indent=2, ensure_ascii=False)
        print(f"\n📊 训练结果已保存: {results_path}")
    
    # 打印总结
    print_training_summary(training_results, compare_rewards)
    
    return training_results

def print_training_summary(training_results, compare_rewards):
    """打印训练总结"""
    print(f"\n📋 简化奖励函数训练总结")
    print("=" * 60)
    
    # 简化奖励函数结果
    simplified_stages = {k: v for k, v in training_results["stages"].items() if "simplified" in k}
    
    print("🎯 简化奖励函数结果:")
    for stage_key, stage_result in simplified_stages.items():
        if "error" not in stage_result:
            stage_num = stage_key.split("_")[1]
            print(f"  阶段{stage_num}: 成功率 {stage_result['success_rate']:.2%}, "
                  f"平均奖励 {stage_result['final_avg_reward']:.2f}, "
                  f"稳定性(CV) {stage_result['reward_cv']:.3f}")
        else:
            print(f"  {stage_key}: 失败 - {stage_result['error']}")
    
    # 对比结果（如果有）
    if compare_rewards:
        complex_stages = {k: v for k, v in training_results["stages"].items() if "complex" in k}
        
        print("\n🔧 复杂奖励函数对比结果:")
        for stage_key, stage_result in complex_stages.items():
            if "error" not in stage_result:
                stage_num = stage_key.split("_")[1]
                print(f"  阶段{stage_num}: 成功率 {stage_result['success_rate']:.2%}, "
                      f"平均奖励 {stage_result['final_avg_reward']:.2f}, "
                      f"稳定性(CV) {stage_result['reward_cv']:.3f}")
            else:
                print(f"  {stage_key}: 失败 - {stage_result['error']}")
        
        # 改善分析
        print("\n📈 简化奖励函数改善效果:")
        for stage_num in [1, 2, 3]:
            simplified_key = f"stage_{stage_num}_simplified"
            complex_key = f"stage_{stage_num}_complex"
            
            if simplified_key in training_results["stages"] and complex_key in training_results["stages"]:
                simplified = training_results["stages"][simplified_key]
                complex = training_results["stages"][complex_key]
                
                if "error" not in simplified and "error" not in complex:
                    success_improvement = simplified['success_rate'] - complex['success_rate']
                    stability_improvement = complex['reward_cv'] - simplified['reward_cv']
                    
                    print(f"  阶段{stage_num}: 成功率改善 {success_improvement:+.3f}, "
                          f"稳定性改善 {stability_improvement:+.3f}")

def main():
    parser = argparse.ArgumentParser(description='简化奖励函数分阶段训练')
    parser.add_argument('--start-stage', type=int, default=1, choices=[1, 2, 3],
                       help='开始阶段 (1: 基础, 2: 复杂静态, 3: 动态适应)')
    parser.add_argument('--end-stage', type=int, default=3, choices=[1, 2, 3],
                       help='结束阶段 (1: 基础, 2: 复杂静态, 3: 动态适应)')
    parser.add_argument('--no-save', action='store_true', help='禁用结果保存')
    parser.add_argument('--compare', action='store_true', help='同时训练复杂奖励函数进行对比')
    
    args = parser.parse_args()
    
    # 显示训练计划
    print("📋 训练计划:")
    print("=" * 30)
    stage_names = {1: "基础训练", 2: "复杂静态", 3: "动态适应"}
    for stage in range(args.start_stage, args.end_stage + 1):
        print(f"阶段 {stage}: {stage_names[stage]} (简化奖励)")
        if args.compare:
            print(f"       + {stage_names[stage]} (复杂奖励对比)")
    print()
    
    # 执行训练
    results = simplified_reward_staged_training(
        start_stage=args.start_stage,
        end_stage=args.end_stage,
        save_outputs=not args.no_save,
        compare_rewards=args.compare
    )
    
    print("\n✅ 简化奖励函数分阶段训练完成!")
    print("🎯 主要优势:")
    print("  • 学习目标明确：主要优化全局路径")
    print("  • 奖励信号强烈：±100终止奖励")
    print("  • 训练稳定：减少多目标冲突")
    print("  • 收敛更快：单一主导目标")

if __name__ == "__main__":
    main()
