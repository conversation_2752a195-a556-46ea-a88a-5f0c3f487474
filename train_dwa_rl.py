"""
独立的DWA-RL训练脚本
包含完整的训练功能、3D可视化和结果保存
"""

import numpy as np
import matplotlib
matplotlib.use('TkAgg')  # Force TkAgg backend
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import json
import os
import time
import argparse
from datetime import datetime
from collections import deque

from dwa_rl_core import StabilizedEnvironment, StabilizedTD3Controller, td3_config

def train_dwa_rl_model(num_episodes=200, enable_visualization=True, save_outputs=True, environment_config=None):
    """训练DWA-RL模型"""
    print("🚀 DWA-RL模型训练")
    print("=" * 60)
    print("训练配置:")
    print(f"• Episodes: {num_episodes}")
    print(f"• 3D可视化: {'启用' if enable_visualization else '禁用'}")
    print(f"• 结果保存: {'启用' if save_outputs else '禁用'}")
    print(f"• 环境配置: {environment_config if environment_config else 'default'}")
    print("• 简化奖励函数 (论文风格)")
    print("• 降低学习率和更新频率")
    print("• 即时训练更新")
    print("=" * 60)

    # 创建输出目录
    if save_outputs and not os.path.exists('training_outputs'):
        os.makedirs('training_outputs')

    # 根据环境配置创建环境
    if environment_config:
        from environment_config import get_environment_config
        env_config = get_environment_config(environment_config)
        env = StabilizedEnvironment(
            enable_dynamic_obstacles=env_config.get('enable_dynamic_obstacles', False),
            environment_config=environment_config
        )
        print(f"✅ 使用环境配置: {environment_config} - {env_config['description']}")
    else:
        env = StabilizedEnvironment(enable_dynamic_obstacles=False, environment_config=None)
        print("✅ 使用默认环境配置")

    controller = StabilizedTD3Controller(td3_config)

    # 训练统计
    episode_rewards = []
    episode_steps = []
    episode_trajectories = []
    step_rewards_all = []
    episode_step_rewards = []  # 每个episode的单步奖励列表
    success_count = 0
    collision_count = 0
    timeout_count = 0

    # 约束量统计
    all_constraint_data = {
        'velocities': [],
        'accelerations': [],
        'obstacle_distances': [],
        'goal_distances': [],
        'constraint_summaries': []
    }

    # 3D可视化设置
    fig_3d = None
    ax_3d = None
    if enable_visualization:
        try:
            plt.ion()  # 开启交互模式
            fig_3d, ax_3d = plt.subplots(1, 1, figsize=(12, 10), subplot_kw={'projection': '3d'})
            fig_3d.suptitle('DWA-RL Training - 3D Trajectory Monitor', fontsize=14)

            # 设置3D环境
            start = env.start
            goal = env.goal
            ax_3d.scatter(start[0], start[1], start[2], c='green', s=200, marker='o', label='Start', alpha=0.8)
            ax_3d.scatter(goal[0], goal[1], goal[2], c='red', s=200, marker='*', label='Goal', alpha=0.8)

            # 绘制障碍物球体
            for i, obs in enumerate(env.obstacles):
                center = obs['center']
                radius = obs['radius']
                u = np.linspace(0, 2 * np.pi, 10)
                v = np.linspace(0, np.pi, 10)
                x = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
                y = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
                z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]
                ax_3d.plot_surface(x, y, z, alpha=0.3, color='gray')

            ax_3d.set_xlabel('X (m)')
            ax_3d.set_ylabel('Y (m)')
            ax_3d.set_zlabel('Z (m)')
            ax_3d.set_title('UAV 3D Trajectory')
            ax_3d.legend()
            ax_3d.set_xlim(0, 100)
            ax_3d.set_ylim(0, 100)
            ax_3d.set_zlim(0, 100)
            ax_3d.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.draw()
            plt.pause(0.01)
            print("✅ 3D Visualization initialized")

        except Exception as e:
            print(f"⚠️ Visualization failed: {e}")
            enable_visualization = False
            fig_3d = None
            ax_3d = None

        # 轨迹颜色
        trajectory_colors = ['blue', 'orange', 'purple', 'brown', 'pink', 'olive', 'cyan', 'magenta']

    start_time = time.time()
    print(f"\n📈 Training started...")
    print("-" * 60)

    for episode in range(num_episodes):
        state = env.reset()
        full_state = np.concatenate([env.state, state[6:]])

        episode_reward = 0
        step_count = 0
        current_episode_step_rewards = []  # 当前episode的单步奖励
        trajectory = []  # 记录轨迹

        while step_count < 500:
            # 记录轨迹点
            trajectory.append(env.state[:3].copy())

            # 获取动作（训练时使用噪声）
            action, info, safe_actions = controller.get_action_with_quality(
                full_state, env.goal, env.obstacles, add_noise=(episode > 10)
            )

            # 执行动作
            next_state, reward, done, env_info = env.step(action)
            next_full_state = np.concatenate([env.state, next_state[6:]])

            # 添加质量奖励
            quality_bonus = info.get('quality_score', 1.0) * 0.05  # 减少质量奖励影响
            total_reward = reward + quality_bonus

            # 记录奖励
            controller.step_rewards.append(total_reward)
            step_rewards_all.append(total_reward)
            current_episode_step_rewards.append(total_reward)

            # 存储经验
            controller.replay_buffer.add(
                full_state.copy(),
                action.copy(),
                total_reward,
                next_full_state.copy(),
                done,
                safe_actions,
                env.goal.copy(),
                [obs.copy() for obs in env.obstacles],
                info.get('selected_idx', 0)
            )

            # 即时训练更新
            _ = controller.immediate_update(batch_size=32)

            episode_reward += total_reward
            step_count += 1

            if done:
                # 记录episode结果
                if env_info.get('success', False):
                    success_count += 1
                elif env_info.get('collision', False):
                    collision_count += 1
                elif env_info.get('timeout', False):
                    timeout_count += 1
                break

            full_state = next_full_state

        # 收集约束数据
        constraint_summary = env.get_constraint_summary()
        if constraint_summary:
            all_constraint_data['velocities'].extend(env.constraint_history['velocities'])
            all_constraint_data['accelerations'].extend(env.constraint_history['accelerations'])
            all_constraint_data['obstacle_distances'].extend(env.constraint_history['distances_to_obstacles'])
            all_constraint_data['goal_distances'].extend(env.constraint_history['goal_distances'])
            all_constraint_data['constraint_summaries'].append(constraint_summary)

        episode_rewards.append(episode_reward)
        episode_steps.append(step_count)
        episode_step_rewards.append(current_episode_step_rewards.copy())
        episode_trajectories.append(trajectory)

        # 更新3D可视化
        if enable_visualization and fig_3d is not None and ax_3d is not None:
            try:
                # 绘制轨迹
                if trajectory and len(trajectory) > 1:
                    positions = np.array(trajectory)
                    color = trajectory_colors[episode % len(trajectory_colors)]

                    # 根据结果设置轨迹样式
                    if env_info.get('success', False):
                        alpha = 0.8
                        linewidth = 2
                        linestyle = '-'
                    elif env_info.get('collision', False):
                        alpha = 0.6
                        linewidth = 1.5
                        linestyle = '--'
                        color = 'red'
                    else:
                        alpha = 0.4
                        linewidth = 1
                        linestyle = ':'

                    # 在episodes 11, 21, 31, ...时清除旧轨迹
                    if episode > 0 and (episode + 1) % 10 == 1 and episode >= 10:
                        ax_3d.clear()

                        # 重新绘制基础元素
                        start = env.start
                        goal = env.goal
                        ax_3d.scatter(start[0], start[1], start[2], c='green', s=200, marker='o', label='Start', alpha=0.8)
                        ax_3d.scatter(goal[0], goal[1], goal[2], c='red', s=200, marker='*', label='Goal', alpha=0.8)

                        # 重新绘制障碍物
                        for obs in env.obstacles:
                            center = obs['center']
                            radius = obs['radius']
                            u = np.linspace(0, 2 * np.pi, 10)
                            v = np.linspace(0, np.pi, 10)
                            x = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
                            y = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
                            z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]
                            ax_3d.plot_surface(x, y, z, alpha=0.3, color='gray')

                        ax_3d.set_xlabel('X (m)')
                        ax_3d.set_ylabel('Y (m)')
                        ax_3d.set_zlabel('Z (m)')
                        ax_3d.set_xlim(0, 100)
                        ax_3d.set_ylim(0, 100)
                        ax_3d.set_zlim(0, 100)
                        ax_3d.grid(True, alpha=0.3)
                        ax_3d.legend()

                    ax_3d.plot(positions[:, 0], positions[:, 1], positions[:, 2],
                             color=color, alpha=alpha, linewidth=linewidth, linestyle=linestyle)

                # 更新标题显示实时统计
                success_rate = success_count / (episode + 1)
                avg_reward = np.mean(episode_rewards[-5:]) if len(episode_rewards) >= 5 else (np.mean(episode_rewards) if episode_rewards else 0)
                ax_3d.set_title(f'Episode {episode + 1}/{num_episodes} | Success Rate: {success_rate:.2%} | '
                              f'Avg Reward: {avg_reward:.1f} | Buffer: {len(controller.replay_buffer)}')

                # 更新显示
                try:
                    fig_3d.canvas.draw_idle()
                    plt.pause(0.001)
                except Exception:
                    enable_visualization = False
                    print("⚠️ Visualization disabled due to GUI error")

            except Exception as e:
                print(f"⚠️ Visualization update failed: {e}")
                enable_visualization = False

        # 每5个episode报告一次
        if episode % 5 == 0:
            recent_episodes = episode_rewards[-5:] if len(episode_rewards) >= 5 else episode_rewards
            avg_reward = np.mean(recent_episodes)
            std_reward = np.std(recent_episodes)
            success_rate = success_count / (episode + 1)

            print(f"Episode {episode:2d}: Reward={avg_reward:6.2f}±{std_reward:5.2f}, Success={success_rate:.3f}, Steps={step_count:3d}")

    # 关闭交互模式
    if enable_visualization:
        plt.ioff()

    training_time = time.time() - start_time

    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 保存3D可视化图像
    if enable_visualization and fig_3d is not None and save_outputs:
        try:
            plot_file = f'training_outputs/training_3d_{timestamp}.png'
            fig_3d.suptitle(f'DWA-RL Training Results\n'
                        f'Success Rate: {success_count/num_episodes:.1%} | '
                        f'Avg Reward: {np.mean(episode_rewards):.1f} | '
                        f'CV: {np.std(episode_rewards)/abs(np.mean(episode_rewards)):.3f}',
                        fontsize=14)

            fig_3d.savefig(plot_file, dpi=300, bbox_inches='tight', facecolor='white')
            print(f"✅ 3D trajectory plot saved: {plot_file}")

        except Exception as e:
            print(f"⚠️ 3D plot saving failed: {e}")

    # 分析结果
    print(f"\n📊 Training Results:")
    print(f"Training Time: {training_time:.2f} seconds")
    print(f"Total Episodes: {num_episodes}")
    print(f"Success Rate: {success_count/num_episodes:.3f} ({success_count}/{num_episodes})")
    print(f"Collision Rate: {collision_count/num_episodes:.3f} ({collision_count}/{num_episodes})")
    print(f"Timeout Rate: {timeout_count/num_episodes:.3f} ({timeout_count}/{num_episodes})")
    print(f"Average Episode Reward: {np.mean(episode_rewards):.2f}")
    print(f"Reward Standard Deviation: {np.std(episode_rewards):.2f}")
    print(f"Coefficient of Variation: {np.std(episode_rewards)/abs(np.mean(episode_rewards)):.3f}")
    print(f"Average Steps per Episode: {np.mean(episode_steps):.1f}")
    print(f"Average Step Reward: {np.mean(step_rewards_all):.3f}")

    if save_outputs:
        # 保存模型
        model_path = f'training_outputs/dwa_rl_model_{timestamp}.pth'
        controller.save_model(model_path)
        print(f"✅ 模型已保存: {model_path}")

        # 保存训练数据
        save_training_data(episode_rewards, episode_steps, step_rewards_all,
                          episode_step_rewards, episode_trajectories, success_count,
                          collision_count, timeout_count, num_episodes, timestamp, training_time,
                          all_constraint_data)

    return episode_rewards, step_rewards_all, controller, all_constraint_data

def save_training_data(episode_rewards, episode_steps, step_rewards_all,
                      episode_step_rewards, episode_trajectories, success_count,
                      collision_count, timeout_count, num_episodes, timestamp, training_time,
                      constraint_data):
    """保存训练数据到JSON文件"""

    # 创建详细的训练报告
    training_report = {
        "training_summary": {
            "training_type": "dwa_rl_training",
            "total_episodes": num_episodes,
            "training_time_seconds": training_time,
            "timestamp": datetime.now().isoformat(),
            "features": [
                "简化奖励函数",
                "降低学习率",
                "即时训练更新",
                "3D可视化",
                "经验回放缓冲区"
            ]
        },
        "performance_metrics": {
            "final_success_rate": round(success_count / num_episodes, 4),
            "collision_rate": round(collision_count / num_episodes, 4),
            "timeout_rate": round(timeout_count / num_episodes, 4),
            "average_episode_reward": round(np.mean(episode_rewards), 3),
            "episode_reward_std": round(np.std(episode_rewards), 3),
            "coefficient_of_variation": round(np.std(episode_rewards)/abs(np.mean(episode_rewards)), 4),
            "max_episode_reward": round(np.max(episode_rewards), 3),
            "min_episode_reward": round(np.min(episode_rewards), 3),
            "average_steps_per_episode": round(np.mean(episode_steps), 1),
            "average_step_reward": round(np.mean(step_rewards_all), 4),
            "step_reward_std": round(np.std(step_rewards_all), 4)
        },
        "episode_data": {
            "episode_rewards": [round(r, 3) for r in episode_rewards],
            "episode_steps": episode_steps,
            "total_steps": len(step_rewards_all)
        },
        "step_data": {
            "all_step_rewards": [round(r, 4) for r in step_rewards_all],
            "episode_step_rewards": [[round(r, 4) for r in ep_rewards] for ep_rewards in episode_step_rewards]
        }
    }

    # 保存JSON报告
    json_path = f'training_outputs/training_report_{timestamp}.json'
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(training_report, f, indent=2, ensure_ascii=False)

    print(f"✅ Training report saved: {json_path}")

    # 保存简化的CSV格式
    csv_path = f'training_outputs/training_rewards_{timestamp}.csv'
    with open(csv_path, 'w', encoding='utf-8') as f:
        f.write("Episode,Episode_Reward,Steps,Step_Rewards\n")
        for i, (ep_reward, steps, step_rewards) in enumerate(zip(episode_rewards, episode_steps, episode_step_rewards)):
            step_rewards_str = ';'.join([f"{r:.4f}" for r in step_rewards])
            f.write(f"{i+1},{ep_reward:.3f},{steps},\"{step_rewards_str}\"\n")

    print(f"✅ CSV data saved: {csv_path}")

    return training_report

def create_constraint_analysis(constraint_data, timestamp, phase="training"):
    """创建约束量分析图表"""
    if not constraint_data['velocities']:
        print("⚠️ 没有约束数据可供分析")
        return

    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f'DWA-RL {phase.title()} - Constraint Analysis', fontsize=16, fontweight='bold')

    velocities = np.array(constraint_data['velocities'])
    accelerations = np.array(constraint_data['accelerations'])
    obstacle_distances = np.array(constraint_data['obstacle_distances'])
    goal_distances = np.array(constraint_data['goal_distances'])

    # Constraint limit lines - using mathematically consistent constraint values
    max_velocity_components = [3.0, 3.0, 3.0]  # DWA component constraints
    max_velocity = np.sqrt(sum([v**2 for v in max_velocity_components]))  # Corresponding total velocity constraint
    max_acceleration_components = [5.0, 5.0, 5.0]  # DWA component constraints
    max_acceleration = np.sqrt(sum([a**2 for a in max_acceleration_components]))  # Corresponding total acceleration constraint
    min_obstacle_distance = 3.0

    # 1. Velocity time series
    ax1 = axes[0, 0]
    time_steps = np.arange(len(velocities))
    ax1.plot(time_steps, velocities, 'b-', alpha=0.7, linewidth=1)
    ax1.axhline(y=max_velocity, color='red', linestyle='--', linewidth=2, label=f'Total Velocity Limit ({max_velocity:.2f} m/s)')
    ax1.axhline(y=max_velocity_components[0], color='orange', linestyle=':', linewidth=1, label=f'Component Limit ({max_velocity_components[0]} m/s)')
    ax1.fill_between(time_steps, 0, max_velocity, alpha=0.2, color='green', label='Safe Zone')
    ax1.fill_between(time_steps, max_velocity, velocities.max()*1.1, alpha=0.2, color='red', label='Violation Zone')
    ax1.set_title('Velocity Time Series')
    ax1.set_xlabel('Time Steps')
    ax1.set_ylabel('Velocity (m/s)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. Acceleration time series
    ax2 = axes[0, 1]
    ax2.plot(time_steps, accelerations, 'g-', alpha=0.7, linewidth=1)
    ax2.axhline(y=max_acceleration, color='red', linestyle='--', linewidth=2, label=f'Total Acceleration Limit ({max_acceleration:.2f} m/s²)')
    ax2.axhline(y=max_acceleration_components[0], color='orange', linestyle=':', linewidth=1, label=f'Component Limit ({max_acceleration_components[0]} m/s²)')
    ax2.fill_between(time_steps, 0, max_acceleration, alpha=0.2, color='green', label='Safe Zone')
    ax2.fill_between(time_steps, max_acceleration, accelerations.max()*1.1, alpha=0.2, color='red', label='Violation Zone')
    ax2.set_title('Acceleration Time Series')
    ax2.set_xlabel('Time Steps')
    ax2.set_ylabel('Acceleration (m/s²)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 3. Obstacle distance time series
    ax3 = axes[0, 2]
    ax3.plot(time_steps, obstacle_distances, 'orange', alpha=0.7, linewidth=1)
    ax3.axhline(y=min_obstacle_distance, color='red', linestyle='--', linewidth=2, label=f'Min Safe Distance ({min_obstacle_distance} m)')
    ax3.fill_between(time_steps, min_obstacle_distance, obstacle_distances.max()*1.1, alpha=0.2, color='green', label='Safe Zone')
    ax3.fill_between(time_steps, 0, min_obstacle_distance, alpha=0.2, color='red', label='Danger Zone')
    ax3.set_title('Obstacle Distance Time Series')
    ax3.set_xlabel('Time Steps')
    ax3.set_ylabel('Min Obstacle Distance (m)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 4. Velocity distribution histogram
    ax4 = axes[1, 0]
    ax4.hist(velocities, bins=30, alpha=0.7, color='blue', edgecolor='black')
    ax4.axvline(x=max_velocity, color='red', linestyle='--', linewidth=2, label=f'Total Velocity Limit ({max_velocity:.2f} m/s)')
    ax4.axvline(x=max_velocity_components[0], color='orange', linestyle=':', linewidth=1, label=f'Component Limit ({max_velocity_components[0]} m/s)')
    ax4.axvline(x=np.mean(velocities), color='green', linestyle='-', linewidth=2, label=f'Mean Velocity ({np.mean(velocities):.2f} m/s)')
    violation_rate = np.sum(velocities > max_velocity) / len(velocities) * 100
    ax4.set_title(f'Velocity Distribution (Total Violation Rate: {violation_rate:.1f}%)')
    ax4.set_xlabel('Velocity (m/s)')
    ax4.set_ylabel('Frequency')
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    # 5. Acceleration distribution histogram
    ax5 = axes[1, 1]
    ax5.hist(accelerations, bins=30, alpha=0.7, color='green', edgecolor='black')
    ax5.axvline(x=max_acceleration, color='red', linestyle='--', linewidth=2, label=f'Total Acceleration Limit ({max_acceleration:.2f} m/s²)')
    ax5.axvline(x=max_acceleration_components[0], color='orange', linestyle=':', linewidth=1, label=f'Component Limit ({max_acceleration_components[0]} m/s²)')
    ax5.axvline(x=np.mean(accelerations), color='blue', linestyle='-', linewidth=2, label=f'Mean Acceleration ({np.mean(accelerations):.2f} m/s²)')
    violation_rate = np.sum(accelerations > max_acceleration) / len(accelerations) * 100
    ax5.set_title(f'Acceleration Distribution (Total Violation Rate: {violation_rate:.1f}%)')
    ax5.set_xlabel('Acceleration (m/s²)')
    ax5.set_ylabel('Frequency')
    ax5.legend()
    ax5.grid(True, alpha=0.3)

    # 6. Constraint violation statistics
    ax6 = axes[1, 2]
    if constraint_data['constraint_summaries']:
        summaries = constraint_data['constraint_summaries']
        velocity_violations = [s['velocity_stats']['magnitude_violations'] for s in summaries]
        acceleration_violations = [s['acceleration_stats']['magnitude_violations'] for s in summaries]
        obstacle_violations = [s['obstacle_distance_stats']['violations'] for s in summaries]

        episodes = np.arange(len(summaries))
        width = 0.25

        ax6.bar(episodes - width, velocity_violations, width, label='Velocity Violations', color='blue', alpha=0.7)
        ax6.bar(episodes, acceleration_violations, width, label='Acceleration Violations', color='green', alpha=0.7)
        ax6.bar(episodes + width, obstacle_violations, width, label='Obstacle Violations', color='orange', alpha=0.7)

        ax6.set_title('Constraint Violations per Episode')
        ax6.set_xlabel('Episode')
        ax6.set_ylabel('Violation Count')
        ax6.legend()
        ax6.grid(True, alpha=0.3)

    plt.tight_layout()

    # 保存图表
    constraint_plot_path = f'training_outputs/{phase}_constraint_analysis_{timestamp}.png'
    plt.savefig(constraint_plot_path, dpi=300, bbox_inches='tight')
    print(f"✅ 约束分析图已保存: {constraint_plot_path}")

    plt.show()

    # Print constraint statistics summary
    print(f"\n📊 {phase.title()} Constraint Statistics Summary (DWA-Environment Constraint Consistency Fixed):")
    print("=" * 80)
    print(f"Total Time Steps: {len(velocities)}")
    print(f"Velocity Constraint Statistics:")
    print(f"  - DWA Component Constraints: {max_velocity_components} m/s")
    print(f"  - Corresponding Total Velocity Constraint: {max_velocity:.2f} m/s")
    print(f"  - Actual Maximum: {np.max(velocities):.2f} m/s")
    print(f"  - Average: {np.mean(velocities):.2f} m/s")
    print(f"  - Total Velocity Violations: {np.sum(velocities > max_velocity)} ({np.sum(velocities > max_velocity)/len(velocities)*100:.1f}%)")
    print(f"Acceleration Constraint Statistics:")
    print(f"  - DWA Component Constraints: {max_acceleration_components} m/s²")
    print(f"  - Corresponding Total Acceleration Constraint: {max_acceleration:.2f} m/s²")
    print(f"  - Actual Maximum: {np.max(accelerations):.2f} m/s²")
    print(f"  - Average: {np.mean(accelerations):.2f} m/s²")
    print(f"  - Total Acceleration Violations: {np.sum(accelerations > max_acceleration)} ({np.sum(accelerations > max_acceleration)/len(accelerations)*100:.1f}%)")
    print(f"障碍物距离统计:")
    print(f"  - 最小值: {np.min(obstacle_distances):.2f} m (安全距离: {min_obstacle_distance} m)")
    print(f"  - 平均值: {np.mean(obstacle_distances):.2f} m")
    print(f"  - 违反次数: {np.sum(obstacle_distances < min_obstacle_distance)} ({np.sum(obstacle_distances < min_obstacle_distance)/len(obstacle_distances)*100:.1f}%)")

    return constraint_plot_path

def create_training_plots(episode_rewards, step_rewards_all, episode_step_rewards, timestamp):
    """创建训练分析图表"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('DWA-RL Training Analysis', fontsize=16, fontweight='bold')

    # 1. Episode奖励趋势
    axes[0, 0].plot(episode_rewards, 'b-o', alpha=0.7, markersize=4)
    # 添加趋势线
    z = np.polyfit(range(len(episode_rewards)), episode_rewards, 1)
    p = np.poly1d(z)
    axes[0, 0].plot(range(len(episode_rewards)), p(range(len(episode_rewards))), "r--", alpha=0.8,
                   label=f'Trend: {z[0]:.2f}x+{z[1]:.2f}')
    axes[0, 0].axhline(np.mean(episode_rewards), color='green', linestyle='--',
                      label=f'Mean: {np.mean(episode_rewards):.1f}')
    axes[0, 0].set_title('Episode Rewards with Trend')
    axes[0, 0].set_xlabel('Episode')
    axes[0, 0].set_ylabel('Reward')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # 2. 步级奖励分布
    axes[0, 1].hist(step_rewards_all, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 1].axvline(np.mean(step_rewards_all), color='red', linestyle='--',
                      label=f'Mean: {np.mean(step_rewards_all):.3f}')
    axes[0, 1].set_title('Step Reward Distribution')
    axes[0, 1].set_xlabel('Step Reward')
    axes[0, 1].set_ylabel('Frequency')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # 3. 平均步级奖励进展
    avg_step_rewards = []
    for i, step_rewards in enumerate(episode_step_rewards):
        if step_rewards:
            avg_step_rewards.append(np.mean(step_rewards))
        else:
            avg_step_rewards.append(0)

    axes[0, 2].plot(avg_step_rewards, 'g-', alpha=0.7, label='Episode Avg Step Reward')
    axes[0, 2].set_title('Average Step Reward Progress')
    axes[0, 2].set_xlabel('Episode')
    axes[0, 2].set_ylabel('Avg Step Reward')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)

    # 4. 奖励变异系数
    window_size = max(1, len(episode_rewards) // 10)
    if len(episode_rewards) >= window_size:
        rolling_cv = []
        for i in range(window_size, len(episode_rewards) + 1):
            window_rewards = episode_rewards[i-window_size:i]
            cv = np.std(window_rewards) / abs(np.mean(window_rewards)) if np.mean(window_rewards) != 0 else 0
            rolling_cv.append(cv)

        axes[1, 0].plot(range(window_size, len(episode_rewards) + 1), rolling_cv, 'purple', linewidth=2)
        axes[1, 0].set_title(f'Rolling Coefficient of Variation (window={window_size})')
        axes[1, 0].set_xlabel('Episode')
        axes[1, 0].set_ylabel('CV')
        axes[1, 0].grid(True, alpha=0.3)

    # 5. 累积奖励
    cumulative_rewards = np.cumsum(episode_rewards)
    axes[1, 1].plot(cumulative_rewards, 'orange', linewidth=2)
    axes[1, 1].set_title('Cumulative Rewards')
    axes[1, 1].set_xlabel('Episode')
    axes[1, 1].set_ylabel('Cumulative Reward')
    axes[1, 1].grid(True, alpha=0.3)

    # 6. 前1000步奖励详情
    first_1000_rewards = step_rewards_all[:1000] if len(step_rewards_all) >= 1000 else step_rewards_all
    axes[1, 2].plot(first_1000_rewards, 'brown', alpha=0.7)
    axes[1, 2].set_title('First 1000 Step Rewards')
    axes[1, 2].set_xlabel('Step')
    axes[1, 2].set_ylabel('Reward')
    axes[1, 2].grid(True, alpha=0.3)

    plt.tight_layout()

    # 保存图表
    plot_path = f'training_outputs/training_analysis_{timestamp}.png'
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"✅ Training analysis plot saved: {plot_path}")

    plt.show()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='训练DWA-RL模型')
    parser.add_argument('--episodes', type=int, default=200, help='训练episodes数量')
    parser.add_argument('--no-viz', action='store_true', help='禁用3D可视化')
    parser.add_argument('--no-save', action='store_true', help='禁用结果保存')

    args = parser.parse_args()

    print("🚀 DWA-RL独立训练脚本")
    print("=" * 50)

    # 运行训练
    episode_rewards, step_rewards, controller, constraint_data = train_dwa_rl_model(
        num_episodes=args.episodes,
        enable_visualization=not args.no_viz,
        save_outputs=not args.no_save
    )

    # 创建分析图表
    if not args.no_save:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        create_training_plots(episode_rewards, step_rewards,
                            [[r for r in controller.step_rewards]],
                            timestamp)

        # 创建约束量分析
        create_constraint_analysis(constraint_data, timestamp, "training")

    print("\n✅ 训练完成!")
    print(f"📊 最终成功率: {len([r for r in episode_rewards if r > 0])/len(episode_rewards):.2%}")
    print(f"📈 平均奖励: {np.mean(episode_rewards):.2f}")
    print(f"📉 奖励标准差: {np.std(episode_rewards):.2f}")

    if not args.no_save:
        print("\n📁 输出文件:")
        print("• training_outputs/dwa_rl_model_*.pth - 训练好的模型")
        print("• training_outputs/training_report_*.json - 详细训练报告")
        print("• training_outputs/training_rewards_*.csv - 奖励数据")
        print("• training_outputs/training_3d_*.png - 3D轨迹图")
        print("• training_outputs/training_analysis_*.png - 训练分析图")