"""
奖励函数对比测试脚本
验证简化奖励函数 vs 复杂奖励函数的效果
"""

import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import json
import os

from dwa_rl_core import StabilizedEnvironment, StabilizedTD3Controller, td3_config
from simple_environment import SimpleUAVEnvironment

class ComplexRewardEnvironment(StabilizedEnvironment):
    """复杂奖励函数环境（原始版本）"""
    
    def _calculate_reward(self):
        """复杂奖励函数 - 原始版本"""
        pos = self.state[:3]
        vel = self.state[3:6]

        # 1. 目标导向奖励 - 减少放大倍数，增加稳定性
        goal_dist = np.linalg.norm(pos - self.goal)
        prev_goal_dist = getattr(self, '_prev_goal_dist', goal_dist)

        # 距离改善奖励（减少放大倍数，增加限制）
        raw_improvement = prev_goal_dist - goal_dist
        # 限制单步改善奖励，防止异常高值
        distance_improvement = np.clip(raw_improvement * 3.0, -self.max_distance_improvement, self.max_distance_improvement)
        self._prev_goal_dist = goal_dist

        # 到达目标奖励（减少突然的高奖励）
        if goal_dist < 5.0:
            return 50.0, True, {'success': True, 'reason': 'goal_reached'}

        # 2. 碰撞惩罚
        for obs in self.obstacles:
            dist = np.linalg.norm(pos - obs['center'])
            if dist <= obs['radius']:
                return -50.0, True, {'collision': True, 'reason': 'collision'}

        # 3. 边界惩罚
        if (pos < 0).any() or (pos > self.bounds).any():
            return -25.0, True, {'out_of_bounds': True, 'reason': 'out_of_bounds'}

        # 4. 速度奖励
        speed = np.linalg.norm(vel)
        speed_reward = min(speed / 3.0, 1.0) * 0.2

        # 5. 安全奖励
        min_obs_dist = float('inf')
        for obs in self.obstacles:
            dist = np.linalg.norm(pos - obs['center']) - obs['radius']
            min_obs_dist = min(min_obs_dist, dist)

        safety_reward = min(min_obs_dist / 10.0, 1.0) * 0.1

        # 6. 时间惩罚
        time_penalty = -0.001

        # 7. 方向奖励
        if np.linalg.norm(vel) > 0.1:
            goal_direction = (self.goal - pos) / np.linalg.norm(self.goal - pos)
            vel_direction = vel / np.linalg.norm(vel)
            direction_reward = np.dot(goal_direction, vel_direction) * 0.2
        else:
            direction_reward = 0

        # 8. 生存奖励
        survival_reward = 0.1

        # 超时检查
        done = self.step_count >= self.max_steps
        if done:
            info = {'timeout': True, 'reason': 'timeout'}
        else:
            info = {}

        # 总奖励计算
        total_reward = (distance_improvement + speed_reward + safety_reward +
                       time_penalty + direction_reward + survival_reward)

        # 奖励裁剪
        total_reward = np.clip(total_reward, -10.0, 10.0)

        return total_reward, done, info

def run_comparison_test(num_episodes=100, environment_config="simple"):
    """运行对比测试"""
    print(f"🔄 开始奖励函数对比测试 (环境: {environment_config})")
    print("=" * 60)
    
    # 测试结果存储
    results = {
        "simple_reward": {
            "episode_rewards": [],
            "step_rewards": [],
            "success_count": 0,
            "collision_count": 0,
            "timeout_count": 0,
            "avg_steps": 0,
            "final_distances": []
        },
        "complex_reward": {
            "episode_rewards": [],
            "step_rewards": [],
            "success_count": 0,
            "collision_count": 0,
            "timeout_count": 0,
            "avg_steps": 0,
            "final_distances": []
        }
    }
    
    # 创建环境配置
    enable_dynamic = environment_config == "complex_dynamic"
    
    # 1. 测试简化奖励函数
    print("\n📊 测试简化奖励函数...")
    env_simple = StabilizedEnvironment(enable_dynamic_obstacles=enable_dynamic)
    controller_simple = StabilizedTD3Controller(td3_config)
    
    for episode in range(num_episodes):
        state = env_simple.reset()
        episode_reward = 0
        steps = 0
        
        for step in range(500):  # 最大步数
            # 随机策略（为了公平比较）
            action = np.random.uniform(-3, 3, 3)
            
            next_state, reward, done, info = env_simple.step(action)
            episode_reward += reward
            steps += 1
            
            if done:
                break
        
        # 记录结果
        results["simple_reward"]["episode_rewards"].append(episode_reward)
        results["simple_reward"]["step_rewards"].append(episode_reward / steps)
        results["simple_reward"]["final_distances"].append(
            np.linalg.norm(env_simple.state[:3] - env_simple.goal)
        )
        
        if info.get('success', False):
            results["simple_reward"]["success_count"] += 1
        elif info.get('collision', False):
            results["simple_reward"]["collision_count"] += 1
        elif info.get('timeout', False):
            results["simple_reward"]["timeout_count"] += 1
        
        if episode % 20 == 0:
            print(f"简化奖励 - Episode {episode}: 奖励={episode_reward:.2f}, 步数={steps}")
    
    # 2. 测试复杂奖励函数
    print("\n📊 测试复杂奖励函数...")
    env_complex = ComplexRewardEnvironment(enable_dynamic_obstacles=enable_dynamic)
    controller_complex = StabilizedTD3Controller(td3_config)
    
    for episode in range(num_episodes):
        state = env_complex.reset()
        episode_reward = 0
        steps = 0
        
        for step in range(500):  # 最大步数
            # 随机策略（为了公平比较）
            action = np.random.uniform(-3, 3, 3)
            
            next_state, reward, done, info = env_complex.step(action)
            episode_reward += reward
            steps += 1
            
            if done:
                break
        
        # 记录结果
        results["complex_reward"]["episode_rewards"].append(episode_reward)
        results["complex_reward"]["step_rewards"].append(episode_reward / steps)
        results["complex_reward"]["final_distances"].append(
            np.linalg.norm(env_complex.state[:3] - env_complex.goal)
        )
        
        if info.get('success', False):
            results["complex_reward"]["success_count"] += 1
        elif info.get('collision', False):
            results["complex_reward"]["collision_count"] += 1
        elif info.get('timeout', False):
            results["complex_reward"]["timeout_count"] += 1
        
        if episode % 20 == 0:
            print(f"复杂奖励 - Episode {episode}: 奖励={episode_reward:.2f}, 步数={steps}")
    
    # 3. 分析结果
    print("\n📈 分析结果...")
    analyze_results(results, num_episodes)
    
    # 4. 生成可视化
    visualize_results(results, environment_config)
    
    return results

def analyze_results(results, num_episodes):
    """分析对比结果"""
    print("🔍 奖励函数对比分析")
    print("=" * 50)
    
    # 计算统计量
    for reward_type in ["simple_reward", "complex_reward"]:
        data = results[reward_type]
        
        print(f"\n{reward_type.replace('_', ' ').title()}:")
        print(f"  平均episode奖励: {np.mean(data['episode_rewards']):.2f} ± {np.std(data['episode_rewards']):.2f}")
        print(f"  平均step奖励: {np.mean(data['step_rewards']):.4f} ± {np.std(data['step_rewards']):.4f}")
        print(f"  成功率: {data['success_count'] / num_episodes:.2%}")
        print(f"  碰撞率: {data['collision_count'] / num_episodes:.2%}")
        print(f"  超时率: {data['timeout_count'] / num_episodes:.2%}")
        print(f"  平均最终距离: {np.mean(data['final_distances']):.2f}")
        
        # 奖励方差分析
        reward_variance = np.var(data['episode_rewards'])
        print(f"  奖励方差: {reward_variance:.2f}")
        
        # 变异系数（标准差/均值）
        if np.mean(data['episode_rewards']) != 0:
            cv = np.std(data['episode_rewards']) / abs(np.mean(data['episode_rewards']))
            print(f"  变异系数: {cv:.4f}")
    
    # 对比分析
    print(f"\n🎯 关键对比指标:")
    simple_data = results["simple_reward"]
    complex_data = results["complex_reward"]
    
    # 信号强度对比
    simple_signal_strength = np.std(simple_data['episode_rewards'])
    complex_signal_strength = np.std(complex_data['episode_rewards'])
    print(f"  信号强度 (标准差): 简化={simple_signal_strength:.2f}, 复杂={complex_signal_strength:.2f}")
    
    # 成功率对比
    simple_success = simple_data['success_count'] / num_episodes
    complex_success = complex_data['success_count'] / num_episodes
    print(f"  成功率: 简化={simple_success:.2%}, 复杂={complex_success:.2%}")
    
    # 平均奖励对比
    simple_avg = np.mean(simple_data['episode_rewards'])
    complex_avg = np.mean(complex_data['episode_rewards'])
    print(f"  平均奖励: 简化={simple_avg:.2f}, 复杂={complex_avg:.2f}")
    
    # 结论
    print(f"\n💡 结论:")
    if simple_signal_strength > complex_signal_strength:
        print("  ✅ 简化奖励函数提供更强的学习信号")
    else:
        print("  ❌ 复杂奖励函数信号更强")
    
    if simple_success > complex_success:
        print("  ✅ 简化奖励函数成功率更高")
    elif simple_success == complex_success:
        print("  ➡️ 两者成功率相当")
    else:
        print("  ❌ 复杂奖励函数成功率更高")

def visualize_results(results, environment_config):
    """可视化对比结果"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle(f'奖励函数对比测试结果 - {environment_config}', fontsize=16)
    
    # 1. Episode奖励对比
    axes[0, 0].plot(results["simple_reward"]["episode_rewards"], 
                    label='简化奖励函数', alpha=0.7, color='blue')
    axes[0, 0].plot(results["complex_reward"]["episode_rewards"], 
                    label='复杂奖励函数', alpha=0.7, color='red')
    axes[0, 0].set_title('Episode奖励对比')
    axes[0, 0].set_xlabel('Episode')
    axes[0, 0].set_ylabel('总奖励')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 奖励分布直方图
    axes[0, 1].hist(results["simple_reward"]["episode_rewards"], 
                    bins=20, alpha=0.7, label='简化奖励函数', color='blue')
    axes[0, 1].hist(results["complex_reward"]["episode_rewards"], 
                    bins=20, alpha=0.7, label='复杂奖励函数', color='red')
    axes[0, 1].set_title('奖励分布')
    axes[0, 1].set_xlabel('奖励值')
    axes[0, 1].set_ylabel('频次')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 成功率对比
    success_rates = [
        results["simple_reward"]["success_count"] / len(results["simple_reward"]["episode_rewards"]),
        results["complex_reward"]["success_count"] / len(results["complex_reward"]["episode_rewards"])
    ]
    axes[1, 0].bar(['简化奖励函数', '复杂奖励函数'], success_rates, color=['blue', 'red'], alpha=0.7)
    axes[1, 0].set_title('成功率对比')
    axes[1, 0].set_ylabel('成功率')
    axes[1, 0].set_ylim(0, 1)
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 最终距离对比
    axes[1, 1].boxplot([results["simple_reward"]["final_distances"], 
                        results["complex_reward"]["final_distances"]], 
                       labels=['简化奖励函数', '复杂奖励函数'])
    axes[1, 1].set_title('最终距离目标距离')
    axes[1, 1].set_ylabel('距离')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    filename = f'reward_comparison_{environment_config}_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📊 对比图表已保存: {filename}")
    plt.show()

def main():
    """主函数"""
    print("🎯 奖励函数对比测试系统")
    print("=" * 60)
    
    # 测试配置
    test_configs = [
        ("simple", "简单静态环境"),
        ("complex_dynamic", "复杂动态环境")
    ]
    
    all_results = {}
    
    for config_name, description in test_configs:
        print(f"\n🌍 测试环境: {description}")
        results = run_comparison_test(num_episodes=50, environment_config=config_name)
        all_results[config_name] = results
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f'reward_comparison_{config_name}_{timestamp}.json'
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"📄 结果已保存: {results_file}")
    
    # 总结报告
    print("\n📋 总结报告")
    print("=" * 60)
    print("根据测试结果，简化奖励函数的优势:")
    print("1. 🎯 更强的学习信号：±100的终止奖励提供明确指导")
    print("2. 📈 更清晰的优化方向：主要优化接近目标的距离")
    print("3. 🚀 更快的收敛：减少了多目标冲突")
    print("4. 🔧 更简单的调参：只需要调整少数几个核心参数")
    
    print("\n💡 建议:")
    print("1. 在分阶段训练中使用简化奖励函数")
    print("2. 让DWA负责安全约束，RL专注于路径优化")
    print("3. 必要时可以在后期引入少量约束条件")

if __name__ == "__main__":
    main() 