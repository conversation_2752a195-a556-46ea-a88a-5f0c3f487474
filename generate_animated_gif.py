"""
生成DWA-RL导航动图（GIF）
实时显示无人机轨迹、动态障碍物运动、速度和加速度信息
"""

import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.animation as animation
from matplotlib.patches import FancyBboxPatch
import os
import glob
from datetime import datetime

from dwa_rl_core import StabilizedEnvironment, load_trained_model, td3_config

def create_navigation_gif(model_path, max_steps=300, fps=10, enable_dynamic_obstacles=True):
    """创建导航动图"""
    print("🎬 生成DWA-RL导航动图")
    print("=" * 60)
    print(f"模型路径: {model_path}")
    print(f"最大步数: {max_steps}")
    print(f"帧率: {fps} FPS")
    print(f"动态障碍物: {'启用' if enable_dynamic_obstacles else '禁用'}")
    print("=" * 60)

    # 加载模型和环境
    controller = load_trained_model(model_path, td3_config)
    env = StabilizedEnvironment(enable_dynamic_obstacles=enable_dynamic_obstacles)
    
    # 重置环境
    state = env.reset()
    full_state = np.concatenate([env.state, state[6:]])
    
    print(f"🚧 环境设置: {len(env.obstacles)} 个静态障碍物")
    if enable_dynamic_obstacles and hasattr(env, 'dynamic_obstacles'):
        print(f"🔄 动态障碍物: {len(env.dynamic_obstacles)} 个")

    # 收集导航数据
    navigation_data = collect_navigation_data(env, controller, full_state, max_steps)
    
    print(f"📊 收集了 {len(navigation_data['positions'])} 个导航步骤")
    
    # 创建动图
    gif_path = create_gif_animation(env, navigation_data, fps, enable_dynamic_obstacles)
    
    return gif_path

def collect_navigation_data(env, controller, initial_state, max_steps):
    """收集导航过程数据"""
    data = {
        'positions': [],
        'velocities': [],
        'accelerations': [],
        'rewards': [],
        'goal_distances': [],
        'step_times': [],
        'dynamic_obstacle_positions': []
    }
    
    full_state = initial_state.copy()
    prev_velocity = np.zeros(3)
    dt = 0.1  # 时间步长
    
    print("🚀 开始收集导航数据...")
    
    for step in range(max_steps):
        # 记录当前状态
        current_pos = env.state[:3].copy()
        current_velocity = env.state[3:6].copy()
        
        # 计算加速度
        acceleration = (current_velocity - prev_velocity) / dt
        
        # 计算目标距离
        goal_distance = np.linalg.norm(current_pos - env.goal)
        
        # 记录数据
        data['positions'].append(current_pos)
        data['velocities'].append(current_velocity)
        data['accelerations'].append(acceleration)
        data['goal_distances'].append(goal_distance)
        data['step_times'].append(step * dt)
        
        # 记录动态障碍物位置
        if hasattr(env, 'dynamic_obstacles'):
            dynamic_pos = [obs['center'].copy() for obs in env.dynamic_obstacles]
            data['dynamic_obstacle_positions'].append(dynamic_pos)
        else:
            data['dynamic_obstacle_positions'].append([])
        
        # 获取所有障碍物
        all_obstacles = env.obstacles.copy()
        if hasattr(env, 'dynamic_obstacles'):
            all_obstacles.extend(env.dynamic_obstacles)
        
        # 获取动作
        action, _, _ = controller.get_action_with_quality(
            full_state, env.goal, all_obstacles, add_noise=True
        )
        
        # 执行动作
        next_state, reward, done, env_info = env.step(action)
        next_full_state = np.concatenate([env.state, next_state[6:]])
        
        data['rewards'].append(reward)
        
        # 每10步打印进度
        if step % 10 == 0:
            vel_mag = np.linalg.norm(current_velocity)
            acc_mag = np.linalg.norm(acceleration)
            print(f"  步骤 {step:3d}: 位置=[{current_pos[0]:5.1f},{current_pos[1]:5.1f},{current_pos[2]:5.1f}], "
                  f"速度={vel_mag:4.1f}m/s, 加速度={acc_mag:4.1f}m/s², 目标距离={goal_distance:5.1f}m")
        
        if done:
            if env_info.get('success', False):
                print(f"🎯 成功到达目标！用时 {step} 步")
            elif env_info.get('collision', False):
                print(f"💥 发生碰撞！在第 {step} 步")
            break
        
        full_state = next_full_state
        prev_velocity = current_velocity
    
    return data

def create_gif_animation(env, data, fps, enable_dynamic):
    """创建GIF动画"""
    print("🎨 创建GIF动画...")
    
    # 设置图形
    fig = plt.figure(figsize=(16, 12))
    ax = fig.add_subplot(111, projection='3d')
    
    # 初始化绘图元素
    trajectory_line, = ax.plot([], [], [], 'b-', linewidth=3, alpha=0.8, label='UAV Path')
    uav_point = ax.scatter([], [], [], c='blue', s=200, marker='D', 
                          edgecolors='darkblue', linewidth=2, label='UAV')
    
    # 绘制静态环境
    setup_static_environment(ax, env)
    
    # 初始化动态障碍物
    dynamic_surfaces = []
    
    # 创建信息显示文本框
    info_box = ax.text2D(0.02, 0.98, '', transform=ax.transAxes, fontsize=11,
                        verticalalignment='top', fontweight='bold',
                        bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.9))
    
    def animate(frame):
        """动画更新函数"""
        if frame >= len(data['positions']):
            return trajectory_line, uav_point, info_box
        
        # 更新轨迹
        positions = np.array(data['positions'][:frame+1])
        if len(positions) > 1:
            trajectory_line.set_data_3d(positions[:, 0], positions[:, 1], positions[:, 2])
        
        # 更新无人机位置
        current_pos = data['positions'][frame]
        uav_point._offsets3d = ([current_pos[0]], [current_pos[1]], [current_pos[2]])
        
        # 更新动态障碍物
        nonlocal dynamic_surfaces
        # 移除旧的动态障碍物
        for surface in dynamic_surfaces:
            surface.remove()
        dynamic_surfaces.clear()
        
        # 绘制新的动态障碍物位置
        if enable_dynamic and frame < len(data['dynamic_obstacle_positions']):
            dynamic_positions = data['dynamic_obstacle_positions'][frame]
            for i, pos in enumerate(dynamic_positions):
                if hasattr(env, 'dynamic_obstacles') and i < len(env.dynamic_obstacles):
                    radius = env.dynamic_obstacles[i]['radius']
                    u = np.linspace(0, 2 * np.pi, 12)
                    v = np.linspace(0, np.pi, 12)
                    x = radius * np.outer(np.cos(u), np.sin(v)) + pos[0]
                    y = radius * np.outer(np.sin(u), np.sin(v)) + pos[1]
                    z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + pos[2]
                    surface = ax.plot_surface(x, y, z, alpha=0.7, color='orange', 
                                            edgecolor='red', linewidth=0.8)
                    dynamic_surfaces.append(surface)
        
        # 更新信息显示
        velocity = data['velocities'][frame]
        acceleration = data['accelerations'][frame]
        goal_dist = data['goal_distances'][frame]
        reward = data['rewards'][frame] if frame < len(data['rewards']) else 0
        
        vel_magnitude = np.linalg.norm(velocity)
        acc_magnitude = np.linalg.norm(acceleration)
        
        # 格式化信息文本（使用英文避免字体问题）
        info_text = f"""Step: {frame + 1}/{len(data['positions'])}
Time: {data['step_times'][frame]:.1f}s
Position: [{current_pos[0]:.1f}, {current_pos[1]:.1f}, {current_pos[2]:.1f}]
Velocity: {vel_magnitude:.2f} m/s
  Vx: {velocity[0]:+.2f} m/s
  Vy: {velocity[1]:+.2f} m/s
  Vz: {velocity[2]:+.2f} m/s
Acceleration: {acc_magnitude:.2f} m/s²
  Ax: {acceleration[0]:+.2f} m/s²
  Ay: {acceleration[1]:+.2f} m/s²
  Az: {acceleration[2]:+.2f} m/s²
Goal Distance: {goal_dist:.1f} m
Current Reward: {reward:.1f}"""
        
        info_box.set_text(info_text)
        
        # 更新标题
        ax.set_title(f'DWA-RL Dynamic Navigation - Step {frame + 1}', 
                    fontsize=14, fontweight='bold')
        
        return trajectory_line, uav_point, info_box
    
    # 创建动画
    print(f"🎬 创建动画，总帧数: {len(data['positions'])}")
    anim = animation.FuncAnimation(fig, animate, frames=len(data['positions']), 
                                 interval=1000//fps, blit=False, repeat=True)
    
    # 保存为GIF
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    gif_filename = f"dwa_rl_navigation_{timestamp}.gif"
    
    print(f"💾 保存GIF动画: {gif_filename}")
    print("⏳ 这可能需要几分钟时间...")
    
    # 使用pillow writer保存GIF
    writer = animation.PillowWriter(fps=fps)
    anim.save(gif_filename, writer=writer, dpi=100)
    
    plt.close()
    
    print(f"✅ GIF动画已保存: {gif_filename}")
    
    # 生成统计信息
    generate_navigation_stats(data, gif_filename)
    
    return gif_filename

def setup_static_environment(ax, env):
    """设置静态环境元素"""
    # 绘制起点和终点
    start = env.start
    goal = env.goal
    ax.scatter(start[0], start[1], start[2], c='green', s=400, marker='o', 
              label='Start', alpha=0.9, edgecolors='darkgreen', linewidth=3)
    ax.scatter(goal[0], goal[1], goal[2], c='red', s=500, marker='*', 
              label='Goal', alpha=0.9, edgecolors='darkred', linewidth=3)
    
    # 绘制静态障碍物
    for obs in env.obstacles:
        center = obs['center']
        radius = obs['radius']
        u = np.linspace(0, 2 * np.pi, 15)
        v = np.linspace(0, np.pi, 15)
        x = radius * np.outer(np.cos(u), np.sin(v)) + center[0]
        y = radius * np.outer(np.sin(u), np.sin(v)) + center[1]
        z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + center[2]
        ax.plot_surface(x, y, z, alpha=0.4, color='gray', 
                       edgecolor='black', linewidth=0.5)
    
    # 设置坐标轴
    ax.set_xlabel('X (m)', fontsize=12)
    ax.set_ylabel('Y (m)', fontsize=12)
    ax.set_zlabel('Z (m)', fontsize=12)
    ax.set_xlim(0, 100)
    ax.set_ylim(0, 100)
    ax.set_zlim(0, 100)
    ax.grid(True, alpha=0.3)
    ax.legend(loc='upper right', fontsize=10)
    # 使用原始训练/测试的默认视角 (matplotlib默认)
    # 不设置view_init，使用matplotlib默认视角

def generate_navigation_stats(data, gif_filename):
    """生成导航统计信息"""
    positions = np.array(data['positions'])
    velocities = np.array(data['velocities'])
    accelerations = np.array(data['accelerations'])
    
    # 计算统计信息
    total_steps = len(positions)
    total_time = data['step_times'][-1] if data['step_times'] else 0
    
    if len(positions) > 1:
        path_length = np.sum(np.linalg.norm(np.diff(positions, axis=0), axis=1))
        avg_velocity = np.mean([np.linalg.norm(v) for v in velocities])
        max_velocity = np.max([np.linalg.norm(v) for v in velocities])
        avg_acceleration = np.mean([np.linalg.norm(a) for a in accelerations])
        max_acceleration = np.max([np.linalg.norm(a) for a in accelerations])
        
        final_goal_distance = data['goal_distances'][-1]
        initial_goal_distance = data['goal_distances'][0]
        distance_improvement = initial_goal_distance - final_goal_distance
        
        print(f"\n📊 导航统计信息:")
        print("=" * 50)
        print(f"总步数: {total_steps}")
        print(f"总时间: {total_time:.1f}s")
        print(f"路径长度: {path_length:.1f}m")
        print(f"平均速度: {avg_velocity:.2f}m/s")
        print(f"最大速度: {max_velocity:.2f}m/s")
        print(f"平均加速度: {avg_acceleration:.2f}m/s²")
        print(f"最大加速度: {max_acceleration:.2f}m/s²")
        print(f"初始目标距离: {initial_goal_distance:.1f}m")
        print(f"最终目标距离: {final_goal_distance:.1f}m")
        print(f"距离改善: {distance_improvement:.1f}m")
        print(f"GIF文件: {gif_filename}")
        print("=" * 50)

def find_latest_model():
    """查找最新的模型文件"""
    model_files = glob.glob('training_outputs/dwa_rl_model_*.pth')
    if not model_files:
        model_files = glob.glob('training_outputs/stabilized_td3_model_*.pth')

    if model_files:
        return max(model_files, key=os.path.getctime)
    else:
        return None

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='生成DWA-RL导航动图')
    parser.add_argument('--model', type=str, help='模型文件路径')
    parser.add_argument('--steps', type=int, default=300, help='最大步数')
    parser.add_argument('--fps', type=int, default=10, help='动画帧率')
    parser.add_argument('--no-dynamic', action='store_true', help='禁用动态障碍物')
    
    args = parser.parse_args()
    
    # 确定模型路径
    if args.model:
        model_path = args.model
    else:
        model_path = find_latest_model()
        if model_path:
            print(f"🔍 使用最新模型: {model_path}")
        else:
            print("❌ 未找到训练好的模型文件")
            exit(1)
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        exit(1)
    
    # 生成动图
    gif_path = create_navigation_gif(
        model_path=model_path,
        max_steps=args.steps,
        fps=args.fps,
        enable_dynamic_obstacles=not args.no_dynamic
    )
    
    print(f"\n🎉 动图生成完成!")
    print(f"📁 文件位置: {gif_path}")
    print(f"🎬 可以直接播放查看动态导航效果")
