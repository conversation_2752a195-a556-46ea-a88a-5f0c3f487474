"""
完整奖励函数对比训练脚本
一键运行简化和复杂奖励函数的完整分阶段训练
总计4000轮训练：简化版本2000轮 + 复杂版本2000轮
"""

import os
import json
import time
import numpy as np
from datetime import datetime
from train_dwa_rl import train_dwa_rl_model
from environment_config import get_environment_config

class CompleteRewardComparisonTrainer:
    """完整奖励函数对比训练器"""
    
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = f"complete_reward_comparison_{self.timestamp}"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 分阶段配置
        self.stages = {
            1: {"environment": "simple", "episodes": 500, "name": "基础训练"},
            2: {"environment": "complex_static", "episodes": 1000, "name": "复杂静态训练"},
            3: {"environment": "complex_dynamic", "episodes": 500, "name": "动态适应训练"}
        }
        
        self.results = {
            "experiment_info": {
                "timestamp": self.timestamp,
                "total_episodes": 4000,
                "description": "简化vs复杂奖励函数完整分阶段对比训练",
                "stages": self.stages
            },
            "simplified_results": {},
            "complex_results": {},
            "comparison_analysis": {}
        }
        
    def print_header(self):
        """打印训练开始信息"""
        print("🚀 完整奖励函数对比训练")
        print("=" * 80)
        print(f"📅 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"🎯 总训练轮数: 4000轮")
        print("📋 训练计划:")
        print("  简化奖励函数分阶段训练: 2000轮 (500+1000+500)")
        print("  复杂奖励函数分阶段训练: 2000轮 (500+1000+500)")
        print("=" * 80)
        
    def train_reward_type(self, reward_type):
        """训练指定奖励函数类型的完整分阶段"""
        print(f"\n🎯 开始{reward_type.upper()}奖励函数分阶段训练")
        print("=" * 60)
        
        reward_results = {
            "reward_type": reward_type,
            "stages": {},
            "total_episodes": 0,
            "total_training_time": 0,
            "overall_performance": {}
        }
        
        total_start_time = time.time()
        
        # 逐阶段训练
        for stage_num, stage_config in self.stages.items():
            print(f"\n📈 阶段{stage_num}: {stage_config['name']}")
            print(f"环境: {stage_config['environment']}")
            print(f"轮数: {stage_config['episodes']}")
            print(f"奖励函数: {reward_type}")
            print("-" * 40)
            
            stage_start_time = time.time()
            
            try:
                # 执行训练
                episode_rewards, step_rewards, controller, constraint_data = train_dwa_rl_model(
                    num_episodes=stage_config['episodes'],
                    enable_visualization=False,  # 禁用可视化提高速度
                    save_outputs=True,  # 保存训练输出
                    environment_config=stage_config['environment'],
                    reward_type=reward_type
                )
                
                stage_time = time.time() - stage_start_time
                
                # 分析阶段结果
                stage_analysis = self.analyze_stage_results(
                    episode_rewards, step_rewards, constraint_data, 
                    stage_num, stage_config, stage_time
                )
                
                reward_results["stages"][f"stage_{stage_num}"] = stage_analysis
                reward_results["total_episodes"] += len(episode_rewards)
                
                print(f"✅ 阶段{stage_num}完成")
                print(f"   训练时间: {stage_time:.1f}秒")
                print(f"   成功率: {stage_analysis['success_rate']:.3f}")
                print(f"   平均奖励: {stage_analysis['avg_episode_reward']:.2f}")
                print(f"   奖励稳定性(CV): {stage_analysis['reward_cv']:.3f}")
                
                # 保存阶段模型和数据
                self.save_stage_results(reward_type, stage_num, {
                    "episode_rewards": episode_rewards,
                    "step_rewards": step_rewards,
                    "constraint_data": constraint_data,
                    "analysis": stage_analysis
                })
                
            except Exception as e:
                print(f"❌ 阶段{stage_num}训练失败: {e}")
                reward_results["stages"][f"stage_{stage_num}"] = {"error": str(e)}
                break
        
        total_time = time.time() - total_start_time
        reward_results["total_training_time"] = total_time
        
        # 计算整体性能
        if len(reward_results["stages"]) == 3:  # 所有阶段都成功
            reward_results["overall_performance"] = self.calculate_overall_performance(reward_results["stages"])
        
        print(f"\n✅ {reward_type.upper()}奖励函数训练完成!")
        print(f"总训练时间: {total_time:.1f}秒 ({total_time/60:.1f}分钟)")
        
        return reward_results
    
    def analyze_stage_results(self, episode_rewards, step_rewards, constraint_data, stage_num, stage_config, training_time):
        """分析单个阶段的训练结果"""
        episode_rewards = np.array(episode_rewards)
        step_rewards = np.array(step_rewards)
        
        # 计算成功率
        success_count = len([r for r in episode_rewards if r > 0])
        success_rate = success_count / len(episode_rewards)
        
        # 计算学习改善
        if len(episode_rewards) >= 20:
            first_20 = np.mean(episode_rewards[:20])
            last_20 = np.mean(episode_rewards[-20:])
            learning_improvement = (last_20 - first_20) / abs(first_20) if first_20 != 0 else 0
        else:
            learning_improvement = 0
        
        # 寻找收敛点
        convergence_episode = self.find_convergence_point(episode_rewards)
        
        analysis = {
            "stage_info": {
                "stage_number": stage_num,
                "environment": stage_config['environment'],
                "episodes": stage_config['episodes'],
                "training_time": training_time
            },
            "performance_metrics": {
                "success_rate": float(success_rate),
                "avg_episode_reward": float(np.mean(episode_rewards)),
                "std_episode_reward": float(np.std(episode_rewards)),
                "reward_cv": float(np.std(episode_rewards) / abs(np.mean(episode_rewards))) if np.mean(episode_rewards) != 0 else 0,
                "max_episode_reward": float(np.max(episode_rewards)),
                "min_episode_reward": float(np.min(episode_rewards)),
                "avg_step_reward": float(np.mean(step_rewards)),
                "learning_improvement": float(learning_improvement),
                "convergence_episode": int(convergence_episode),
                "final_20_avg": float(np.mean(episode_rewards[-20:])) if len(episode_rewards) >= 20 else float(np.mean(episode_rewards))
            },
            "constraint_analysis": {
                "total_constraint_violations": len([v for v in constraint_data.get('constraint_violations', []) if any(v.values())]),
                "avg_velocity": float(np.mean(constraint_data.get('velocities', [0]))),
                "avg_acceleration": float(np.mean(constraint_data.get('accelerations', [0]))),
                "min_obstacle_distance": float(np.min(constraint_data.get('obstacle_distances', [float('inf')])))
            }
        }
        
        return analysis
    
    def find_convergence_point(self, episode_rewards, window=20, threshold=0.05):
        """寻找收敛点"""
        if len(episode_rewards) < window * 2:
            return len(episode_rewards)
        
        for i in range(window, len(episode_rewards) - window):
            current_window = episode_rewards[i:i+window]
            next_window = episode_rewards[i+window:i+2*window]
            
            current_mean = np.mean(current_window)
            next_mean = np.mean(next_window)
            
            if current_mean != 0:
                relative_change = abs(next_mean - current_mean) / abs(current_mean)
                if relative_change < threshold:
                    return i + window
        
        return len(episode_rewards)
    
    def calculate_overall_performance(self, stages):
        """计算整体性能指标"""
        all_success_rates = [stage["performance_metrics"]["success_rate"] for stage in stages.values()]
        all_reward_cvs = [stage["performance_metrics"]["reward_cv"] for stage in stages.values()]
        all_convergence = [stage["performance_metrics"]["convergence_episode"] for stage in stages.values()]
        
        return {
            "overall_success_rate": float(np.mean(all_success_rates)),
            "overall_stability": float(np.mean(all_reward_cvs)),
            "avg_convergence_speed": float(np.mean(all_convergence)),
            "performance_progression": all_success_rates,
            "stability_progression": all_reward_cvs
        }
    
    def save_stage_results(self, reward_type, stage_num, stage_data):
        """保存单个阶段的详细结果"""
        stage_dir = os.path.join(self.output_dir, f"{reward_type}_stage_{stage_num}")
        os.makedirs(stage_dir, exist_ok=True)
        
        # 保存详细数据
        with open(os.path.join(stage_dir, "stage_data.json"), 'w', encoding='utf-8') as f:
            # 转换numpy数组为列表以便JSON序列化
            json_data = {
                "episode_rewards": [float(r) for r in stage_data["episode_rewards"]],
                "step_rewards": [float(r) for r in stage_data["step_rewards"]],
                "analysis": stage_data["analysis"]
            }
            json.dump(json_data, f, indent=2, ensure_ascii=False)
        
        print(f"💾 阶段{stage_num}详细数据已保存: {stage_dir}")
    
    def generate_comparison_analysis(self):
        """生成最终对比分析"""
        if "error" in str(self.results["simplified_results"]) or "error" in str(self.results["complex_results"]):
            print("⚠️ 部分训练失败，跳过对比分析")
            return
        
        simplified = self.results["simplified_results"]["overall_performance"]
        complex = self.results["complex_results"]["overall_performance"]
        
        comparison = {
            "success_rate_improvement": simplified["overall_success_rate"] - complex["overall_success_rate"],
            "stability_improvement": complex["overall_stability"] - simplified["overall_stability"],
            "convergence_improvement": complex["avg_convergence_speed"] - simplified["avg_convergence_speed"],
            "stage_by_stage_comparison": {}
        }
        
        # 逐阶段对比
        for stage_num in [1, 2, 3]:
            stage_key = f"stage_{stage_num}"
            if stage_key in self.results["simplified_results"]["stages"] and stage_key in self.results["complex_results"]["stages"]:
                simplified_stage = self.results["simplified_results"]["stages"][stage_key]["performance_metrics"]
                complex_stage = self.results["complex_results"]["stages"][stage_key]["performance_metrics"]
                
                comparison["stage_by_stage_comparison"][stage_key] = {
                    "success_rate_improvement": simplified_stage["success_rate"] - complex_stage["success_rate"],
                    "stability_improvement": complex_stage["reward_cv"] - simplified_stage["reward_cv"],
                    "convergence_improvement": complex_stage["convergence_episode"] - simplified_stage["convergence_episode"]
                }
        
        self.results["comparison_analysis"] = comparison
    
    def save_final_results(self):
        """保存最终完整结果"""
        self.results["experiment_info"]["end_time"] = datetime.now().isoformat()
        self.results["experiment_info"]["total_duration"] = time.time() - self.start_time
        
        # 保存完整结果
        results_path = os.path.join(self.output_dir, "complete_comparison_results.json")
        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 完整对比结果已保存: {results_path}")
        
        # 生成简要报告
        self.generate_summary_report()
    
    def generate_summary_report(self):
        """生成简要总结报告"""
        print(f"\n📋 完整训练对比总结")
        print("=" * 80)
        
        if "overall_performance" in self.results["simplified_results"]:
            simplified = self.results["simplified_results"]["overall_performance"]
            print("🎯 简化奖励函数整体表现:")
            print(f"  • 整体成功率: {simplified['overall_success_rate']:.3f}")
            print(f"  • 整体稳定性: {simplified['overall_stability']:.3f}")
            print(f"  • 平均收敛速度: {simplified['avg_convergence_speed']:.0f} episodes")
        
        if "overall_performance" in self.results["complex_results"]:
            complex = self.results["complex_results"]["overall_performance"]
            print("\n🔧 复杂奖励函数整体表现:")
            print(f"  • 整体成功率: {complex['overall_success_rate']:.3f}")
            print(f"  • 整体稳定性: {complex['overall_stability']:.3f}")
            print(f"  • 平均收敛速度: {complex['avg_convergence_speed']:.0f} episodes")
        
        if "comparison_analysis" in self.results and self.results["comparison_analysis"]:
            comp = self.results["comparison_analysis"]
            print("\n📊 简化奖励函数优势:")
            print(f"  • 成功率改善: {comp['success_rate_improvement']:+.3f}")
            print(f"  • 稳定性改善: {comp['stability_improvement']:+.3f}")
            print(f"  • 收敛速度改善: {comp['convergence_improvement']:+.0f} episodes")
        
        total_duration = self.results["experiment_info"]["total_duration"]
        print(f"\n⏱️ 总训练时间: {total_duration:.1f}秒 ({total_duration/60:.1f}分钟)")
        print(f"📁 所有结果已保存到: {self.output_dir}")
    
    def run_complete_training(self):
        """运行完整的对比训练"""
        self.start_time = time.time()
        self.print_header()
        
        try:
            # 训练简化奖励函数
            self.results["simplified_results"] = self.train_reward_type("simplified")
            
            print("\n" + "="*80)
            
            # 训练复杂奖励函数
            self.results["complex_results"] = self.train_reward_type("complex")
            
            # 生成对比分析
            self.generate_comparison_analysis()
            
            # 保存最终结果
            self.save_final_results()
            
            print("\n🎉 完整对比训练成功完成!")
            
        except Exception as e:
            print(f"\n❌ 训练过程中发生错误: {e}")
            # 即使出错也保存已有结果
            self.save_final_results()

def main():
    """主函数 - 一键运行完整对比训练"""
    print("🚀 启动完整奖励函数对比训练")
    print("总计4000轮训练即将开始...")
    
    trainer = CompleteRewardComparisonTrainer()
    trainer.run_complete_training()

if __name__ == "__main__":
    main()
