"""
测试完整对比训练脚本
快速验证所有组件是否正常工作
使用少量轮数进行测试
"""

import os
import sys
from datetime import datetime

def test_imports():
    """测试所有必要的导入"""
    print("🔍 测试导入...")
    
    try:
        from train_dwa_rl import train_dwa_rl_model
        print("✅ train_dwa_rl 导入成功")
    except ImportError as e:
        print(f"❌ train_dwa_rl 导入失败: {e}")
        return False
    
    try:
        from environment_config import get_environment_config
        print("✅ environment_config 导入成功")
    except ImportError as e:
        print(f"❌ environment_config 导入失败: {e}")
        return False
    
    try:
        from dwa_rl_core import StabilizedEnvironment
        print("✅ dwa_rl_core 导入成功")
    except ImportError as e:
        print(f"❌ dwa_rl_core 导入失败: {e}")
        return False
    
    try:
        from complete_reward_comparison_training import CompleteRewardComparisonTrainer
        print("✅ complete_reward_comparison_training 导入成功")
    except ImportError as e:
        print(f"❌ complete_reward_comparison_training 导入失败: {e}")
        return False
    
    return True

def test_environment_creation():
    """测试环境创建"""
    print("\n🔍 测试环境创建...")
    
    try:
        from dwa_rl_core import StabilizedEnvironment
        
        # 测试简化奖励函数环境
        env_simplified = StabilizedEnvironment(
            enable_dynamic_obstacles=False,
            environment_config=None,
            reward_type='simplified'
        )
        print("✅ 简化奖励函数环境创建成功")
        
        # 测试复杂奖励函数环境
        env_complex = StabilizedEnvironment(
            enable_dynamic_obstacles=False,
            environment_config=None,
            reward_type='complex'
        )
        print("✅ 复杂奖励函数环境创建成功")
        
        # 测试环境重置
        obs_s = env_simplified.reset()
        obs_c = env_complex.reset()
        print(f"✅ 环境重置成功，观测维度: {len(obs_s)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 环境创建失败: {e}")
        return False

def test_reward_functions():
    """测试奖励函数计算"""
    print("\n🔍 测试奖励函数计算...")
    
    try:
        from dwa_rl_core import StabilizedEnvironment
        import numpy as np
        
        # 创建测试环境
        env_simplified = StabilizedEnvironment(reward_type='simplified')
        env_complex = StabilizedEnvironment(reward_type='complex')
        
        # 重置环境
        env_simplified.reset()
        env_complex.reset()
        
        # 测试动作
        test_action = np.array([1.0, 1.0, 1.0])
        
        # 执行动作并获取奖励
        obs_s, reward_s, done_s, info_s = env_simplified.step(test_action)
        obs_c, reward_c, done_c, info_c = env_complex.step(test_action)
        
        print(f"✅ 简化奖励函数计算成功: {reward_s:.3f}")
        print(f"✅ 复杂奖励函数计算成功: {reward_c:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 奖励函数测试失败: {e}")
        return False

def test_training_function():
    """测试训练函数"""
    print("\n🔍 测试训练函数...")
    
    try:
        from train_dwa_rl import train_dwa_rl_model
        
        print("测试简化奖励函数训练...")
        episode_rewards, step_rewards, controller, constraint_data = train_dwa_rl_model(
            num_episodes=5,  # 只训练5轮进行测试
            enable_visualization=False,
            save_outputs=False,
            environment_config='simple',
            reward_type='simplified'
        )
        print(f"✅ 简化奖励函数训练测试成功，获得{len(episode_rewards)}个episode奖励")
        
        print("测试复杂奖励函数训练...")
        episode_rewards, step_rewards, controller, constraint_data = train_dwa_rl_model(
            num_episodes=5,  # 只训练5轮进行测试
            enable_visualization=False,
            save_outputs=False,
            environment_config='simple',
            reward_type='complex'
        )
        print(f"✅ 复杂奖励函数训练测试成功，获得{len(episode_rewards)}个episode奖励")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练函数测试失败: {e}")
        return False

def test_complete_trainer():
    """测试完整训练器类"""
    print("\n🔍 测试完整训练器类...")
    
    try:
        from complete_reward_comparison_training import CompleteRewardComparisonTrainer
        
        # 创建训练器实例
        trainer = CompleteRewardComparisonTrainer()
        print("✅ 训练器实例创建成功")
        
        # 测试目录创建
        if os.path.exists(trainer.output_dir):
            print(f"✅ 输出目录创建成功: {trainer.output_dir}")
        else:
            print(f"❌ 输出目录创建失败: {trainer.output_dir}")
            return False
        
        # 测试配置
        print(f"✅ 训练阶段配置: {len(trainer.stages)}个阶段")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整训练器测试失败: {e}")
        return False

def run_mini_comparison():
    """运行迷你对比测试"""
    print("\n🔍 运行迷你对比测试...")
    
    try:
        from complete_reward_comparison_training import CompleteRewardComparisonTrainer
        
        # 创建修改版的训练器，使用很少的轮数
        class MiniTrainer(CompleteRewardComparisonTrainer):
            def __init__(self):
                super().__init__()
                # 修改为很少的轮数进行测试
                self.stages = {
                    1: {"environment": "simple", "episodes": 3, "name": "基础训练测试"},
                    2: {"environment": "complex_static", "episodes": 3, "name": "复杂静态测试"}
                }
        
        trainer = MiniTrainer()
        
        print("开始迷你简化奖励函数训练...")
        simplified_results = trainer.train_reward_type("simplified")
        print("✅ 迷你简化奖励函数训练完成")
        
        print("开始迷你复杂奖励函数训练...")
        complex_results = trainer.train_reward_type("complex")
        print("✅ 迷你复杂奖励函数训练完成")
        
        print("✅ 迷你对比测试成功完成")
        return True
        
    except Exception as e:
        print(f"❌ 迷你对比测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 完整对比训练系统测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tests = [
        ("导入测试", test_imports),
        ("环境创建测试", test_environment_creation),
        ("奖励函数测试", test_reward_functions),
        ("训练函数测试", test_training_function),
        ("完整训练器测试", test_complete_trainer),
        ("迷你对比测试", run_mini_comparison)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪")
        print("✅ 可以运行完整的4000轮对比训练")
        print("\n🚀 运行方式:")
        print("1. 双击 '运行完整对比训练.bat'")
        print("2. 或运行 'python 一键运行完整对比训练.py'")
    else:
        print("⚠️ 部分测试失败，请检查环境配置")
        print("建议先解决测试失败的问题再运行完整训练")
    
    print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
