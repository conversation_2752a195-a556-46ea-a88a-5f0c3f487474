"""
奖励函数对比分析脚本
对比701文件夹中的简化奖励函数与原始复杂奖励函数在分阶段训练中的效果
"""

import numpy as np
import matplotlib.pyplot as plt
import json
import os
from datetime import datetime
import argparse

from train_dwa_rl import train_dwa_rl_model
from environment_config import get_environment_config

def compare_reward_functions(environment_config='simple', episodes=200, save_outputs=True):
    """
    对比简化和复杂奖励函数的训练效果
    
    Args:
        environment_config: 环境配置名称
        episodes: 训练轮数
        save_outputs: 是否保存结果
    """
    print("🔬 奖励函数对比实验")
    print("=" * 60)
    print(f"环境配置: {environment_config}")
    print(f"训练轮数: {episodes}")
    print("=" * 60)
    
    # 创建输出目录
    if save_outputs:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"reward_comparison_{timestamp}"
        os.makedirs(output_dir, exist_ok=True)
        print(f"📁 输出目录: {output_dir}")
    
    # 获取环境信息
    env_config = get_environment_config(environment_config)
    print(f"环境描述: {env_config['description']}")
    
    comparison_results = {
        "experiment_info": {
            "timestamp": timestamp,
            "environment": environment_config,
            "episodes": episodes,
            "description": "简化 vs 复杂奖励函数对比"
        },
        "simplified_results": {},
        "complex_results": {},
        "comparison_analysis": {}
    }
    
    # 训练简化奖励函数
    print(f"\n🎯 训练简化奖励函数")
    print("=" * 40)
    print("奖励函数特点:")
    print("• 距离目标奖励：-goal_dist/50.0")
    print("• 效率激励：-0.1 每步")
    print("• 安全约束：仅危险时惩罚")
    print("• 终止奖励：±100")
    
    try:
        simplified_episode_rewards, simplified_step_rewards, simplified_controller, simplified_constraint_data = train_dwa_rl_model(
            num_episodes=episodes,
            enable_visualization=False,
            save_outputs=False,  # 避免重复保存
            environment_config=environment_config,
            reward_type='simplified'
        )
        
        # 分析简化奖励函数结果
        simplified_analysis = analyze_training_results(
            simplified_episode_rewards, 
            simplified_step_rewards, 
            simplified_constraint_data,
            "simplified"
        )
        comparison_results["simplified_results"] = simplified_analysis
        
        print(f"✅ 简化奖励函数训练完成")
        print(f"   成功率: {simplified_analysis['success_rate']:.3f}")
        print(f"   平均奖励: {simplified_analysis['avg_episode_reward']:.2f}")
        print(f"   奖励稳定性(CV): {simplified_analysis['reward_cv']:.3f}")
        
    except Exception as e:
        print(f"❌ 简化奖励函数训练失败: {e}")
        comparison_results["simplified_results"] = {"error": str(e)}
    
    # 训练复杂奖励函数
    print(f"\n🔧 训练复杂奖励函数")
    print("=" * 40)
    print("奖励函数特点:")
    print("• 距离改善奖励：×10倍放大")
    print("• 目标导向奖励：反比例函数")
    print("• 方向奖励：速度与目标方向点积")
    print("• 进度奖励：距离相关基础奖励")
    print("• 安全奖励：障碍物距离相关")
    print("• 时间惩罚：-0.001 每步")
    
    try:
        complex_episode_rewards, complex_step_rewards, complex_controller, complex_constraint_data = train_dwa_rl_model(
            num_episodes=episodes,
            enable_visualization=False,
            save_outputs=False,  # 避免重复保存
            environment_config=environment_config,
            reward_type='complex'
        )
        
        # 分析复杂奖励函数结果
        complex_analysis = analyze_training_results(
            complex_episode_rewards, 
            complex_step_rewards, 
            complex_constraint_data,
            "complex"
        )
        comparison_results["complex_results"] = complex_analysis
        
        print(f"✅ 复杂奖励函数训练完成")
        print(f"   成功率: {complex_analysis['success_rate']:.3f}")
        print(f"   平均奖励: {complex_analysis['avg_episode_reward']:.2f}")
        print(f"   奖励稳定性(CV): {complex_analysis['reward_cv']:.3f}")
        
    except Exception as e:
        print(f"❌ 复杂奖励函数训练失败: {e}")
        comparison_results["complex_results"] = {"error": str(e)}
    
    # 对比分析
    if "error" not in comparison_results["simplified_results"] and "error" not in comparison_results["complex_results"]:
        comparison_analysis = generate_comparison_analysis(
            comparison_results["simplified_results"],
            comparison_results["complex_results"]
        )
        comparison_results["comparison_analysis"] = comparison_analysis
        
        # 生成对比图表
        if save_outputs:
            generate_comparison_plots(
                simplified_episode_rewards, complex_episode_rewards,
                simplified_step_rewards, complex_step_rewards,
                output_dir
            )
    
    # 保存结果
    if save_outputs:
        results_path = os.path.join(output_dir, "reward_function_comparison.json")
        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(comparison_results, f, indent=2, ensure_ascii=False)
        print(f"\n📊 对比结果已保存: {results_path}")
    
    # 打印对比总结
    print_comparison_summary(comparison_results)
    
    return comparison_results

def analyze_training_results(episode_rewards, step_rewards, constraint_data, reward_type):
    """分析训练结果"""
    episode_rewards = np.array(episode_rewards)
    step_rewards = np.array(step_rewards)
    
    # 计算成功率（假设正奖励表示成功）
    success_count = len([r for r in episode_rewards if r > 0])
    success_rate = success_count / len(episode_rewards)
    
    # 计算学习改善
    if len(episode_rewards) >= 20:
        first_20 = np.mean(episode_rewards[:20])
        last_20 = np.mean(episode_rewards[-20:])
        learning_improvement = (last_20 - first_20) / abs(first_20) if first_20 != 0 else 0
    else:
        learning_improvement = 0
    
    # 寻找收敛点
    convergence_episode = find_convergence_point(episode_rewards)
    
    analysis = {
        "reward_type": reward_type,
        "total_episodes": len(episode_rewards),
        "success_rate": success_rate,
        "avg_episode_reward": float(np.mean(episode_rewards)),
        "std_episode_reward": float(np.std(episode_rewards)),
        "reward_cv": float(np.std(episode_rewards) / abs(np.mean(episode_rewards))) if np.mean(episode_rewards) != 0 else 0,
        "max_episode_reward": float(np.max(episode_rewards)),
        "min_episode_reward": float(np.min(episode_rewards)),
        "avg_step_reward": float(np.mean(step_rewards)),
        "std_step_reward": float(np.std(step_rewards)),
        "learning_improvement": learning_improvement,
        "convergence_episode": convergence_episode,
        "final_20_avg": float(np.mean(episode_rewards[-20:])) if len(episode_rewards) >= 20 else float(np.mean(episode_rewards))
    }
    
    return analysis

def find_convergence_point(episode_rewards, window=20, threshold=0.05):
    """寻找收敛点"""
    if len(episode_rewards) < window * 2:
        return len(episode_rewards)
    
    for i in range(window, len(episode_rewards) - window):
        current_window = episode_rewards[i:i+window]
        next_window = episode_rewards[i+window:i+2*window]
        
        current_mean = np.mean(current_window)
        next_mean = np.mean(next_window)
        
        if current_mean != 0:
            relative_change = abs(next_mean - current_mean) / abs(current_mean)
            if relative_change < threshold:
                return i + window
    
    return len(episode_rewards)

def generate_comparison_analysis(simplified_results, complex_results):
    """生成对比分析"""
    analysis = {
        "success_rate_improvement": simplified_results['success_rate'] - complex_results['success_rate'],
        "reward_stability_improvement": complex_results['reward_cv'] - simplified_results['reward_cv'],
        "convergence_speed_improvement": complex_results['convergence_episode'] - simplified_results['convergence_episode'],
        "learning_improvement_diff": simplified_results['learning_improvement'] - complex_results['learning_improvement'],
        "final_performance_improvement": simplified_results['final_20_avg'] - complex_results['final_20_avg'],
        "avg_reward_difference": simplified_results['avg_episode_reward'] - complex_results['avg_episode_reward']
    }
    
    return analysis

def generate_comparison_plots(simplified_rewards, complex_rewards, simplified_steps, complex_steps, output_dir):
    """生成对比图表"""
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # Episode奖励对比
    ax1.plot(simplified_rewards, label='Simplified Reward Function', color='blue', alpha=0.7)
    ax1.plot(complex_rewards, label='Complex Reward Function', color='red', alpha=0.7)
    ax1.set_title('Episode Rewards Comparison')
    ax1.set_xlabel('Episode')
    ax1.set_ylabel('Episode Reward')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 滑动平均对比
    window = 20
    if len(simplified_rewards) >= window:
        simplified_ma = np.convolve(simplified_rewards, np.ones(window)/window, mode='valid')
        complex_ma = np.convolve(complex_rewards, np.ones(window)/window, mode='valid')
        
        ax2.plot(range(window-1, len(simplified_rewards)), simplified_ma, 
                label='Simplified (Moving Average)', color='blue', linewidth=2)
        ax2.plot(range(window-1, len(complex_rewards)), complex_ma, 
                label='Complex (Moving Average)', color='red', linewidth=2)
    
    ax2.set_title(f'Moving Average Comparison (Window={window})')
    ax2.set_xlabel('Episode')
    ax2.set_ylabel('Average Reward')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Step奖励分布对比
    ax3.hist(simplified_steps, bins=50, alpha=0.7, label='Simplified', color='blue', density=True)
    ax3.hist(complex_steps, bins=50, alpha=0.7, label='Complex', color='red', density=True)
    ax3.set_title('Step Rewards Distribution')
    ax3.set_xlabel('Step Reward')
    ax3.set_ylabel('Density')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 累积奖励对比
    simplified_cumsum = np.cumsum(simplified_rewards)
    complex_cumsum = np.cumsum(complex_rewards)
    
    ax4.plot(simplified_cumsum, label='Simplified (Cumulative)', color='blue')
    ax4.plot(complex_cumsum, label='Complex (Cumulative)', color='red')
    ax4.set_title('Cumulative Rewards Comparison')
    ax4.set_xlabel('Episode')
    ax4.set_ylabel('Cumulative Reward')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'reward_function_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"📈 对比图表已保存: {os.path.join(output_dir, 'reward_function_comparison.png')}")

def print_comparison_summary(comparison_results):
    """打印对比总结"""
    print(f"\n📋 奖励函数对比总结")
    print("=" * 60)
    
    if "error" in comparison_results["simplified_results"]:
        print(f"❌ 简化奖励函数训练失败: {comparison_results['simplified_results']['error']}")
        return
    
    if "error" in comparison_results["complex_results"]:
        print(f"❌ 复杂奖励函数训练失败: {comparison_results['complex_results']['error']}")
        return
    
    simplified = comparison_results["simplified_results"]
    complex = comparison_results["complex_results"]
    comp = comparison_results["comparison_analysis"]
    
    print("🎯 简化奖励函数结果:")
    print(f"  • 成功率: {simplified['success_rate']:.3f}")
    print(f"  • 平均奖励: {simplified['avg_episode_reward']:.2f}")
    print(f"  • 奖励稳定性(CV): {simplified['reward_cv']:.3f}")
    print(f"  • 收敛Episode: {simplified['convergence_episode']}")
    print(f"  • 学习改善: {simplified['learning_improvement']:+.3f}")
    
    print("\n🔧 复杂奖励函数结果:")
    print(f"  • 成功率: {complex['success_rate']:.3f}")
    print(f"  • 平均奖励: {complex['avg_episode_reward']:.2f}")
    print(f"  • 奖励稳定性(CV): {complex['reward_cv']:.3f}")
    print(f"  • 收敛Episode: {complex['convergence_episode']}")
    print(f"  • 学习改善: {complex['learning_improvement']:+.3f}")
    
    print("\n📊 简化奖励函数优势:")
    print(f"  • 成功率改善: {comp['success_rate_improvement']:+.3f}")
    print(f"  • 稳定性改善: {comp['reward_stability_improvement']:+.3f}")
    print(f"  • 收敛速度改善: {comp['convergence_speed_improvement']:+.0f} episodes")
    print(f"  • 学习效果改善: {comp['learning_improvement_diff']:+.3f}")
    print(f"  • 最终性能改善: {comp['final_performance_improvement']:+.2f}")

def main():
    parser = argparse.ArgumentParser(description='奖励函数对比实验')
    parser.add_argument('--environment', type=str, default='simple', 
                       choices=['simple', 'complex_static', 'complex_dynamic'],
                       help='环境配置')
    parser.add_argument('--episodes', type=int, default=200, help='训练轮数')
    parser.add_argument('--no-save', action='store_true', help='禁用结果保存')
    
    args = parser.parse_args()
    
    print("🔬 奖励函数对比实验")
    print(f"环境: {args.environment}")
    print(f"轮数: {args.episodes}")
    print()
    
    results = compare_reward_functions(
        environment_config=args.environment,
        episodes=args.episodes,
        save_outputs=not args.no_save
    )
    
    print("\n✅ 对比实验完成!")

if __name__ == "__main__":
    main()
